"""
测试赖嘉瑶八字的十神权重计算
"""

from shishen_calculator import ShishenCalculator

def test_laijy():
    # 赖嘉瑶的八字
    sizhu = {
        '年柱': ('己', '卯'),
        '月柱': ('甲', '戌'), 
        '日柱': ('辛', '丑'),
        '时柱': ('丙', '申')
    }

    print('=== 赖嘉瑶八字测试 ===')
    print('四柱:', sizhu)
    print('日干:', sizhu['日柱'][0])

    # 测试十神计算  
    shishen_calc = ShishenCalculator()
    shishen_weights = shishen_calc.calculate_shishen_weights(sizhu)
    print('\n十神权重汇总:')
    for shishen, weight in shishen_weights.items():
        print(f'{shishen}: {weight:.2f}')

    # 测试身强身弱计算
    body_analysis = shishen_calc.analyze_body_strength(sizhu)
    print(f'\n身强身弱判断: {body_analysis[0]}')
    print(f'生扶力量: {body_analysis[1]:.2f}')
    print(f'克泄耗力量: {body_analysis[2]:.2f}')
    
    # 手动验证月干权重
    print('\n=== 手动验证月干权重 ===')
    day_gan = sizhu['日柱'][0]  # 辛
    month_gan = sizhu['月柱'][0]  # 甲
    shishen = shishen_calc.determine_shishen(day_gan, month_gan)
    print(f'月干({month_gan}) 对 日干({day_gan}) 的十神关系: {shishen}')
    print(f'月干权重应该是: 1.0 (不应用月干支权重系数)')

    # 喜用神分析
    print('\n=== 喜用神分析 ===')
    xiyong_info = shishen_calc.determine_xiyongshen(body_analysis[0], shishen_weights)
    xiyong_list, ji_list = xiyong_info

    print('喜用神列表（未排序）:')
    for shishen, weight in xiyong_list:
        print(f'  {shishen}: {weight:.2f}')

    print('\n喜用神列表（按权重排序）:')
    sorted_xiyong = sorted(xiyong_list, key=lambda x: x[1], reverse=True)
    for shishen, weight in sorted_xiyong:
        print(f'  {shishen}: {weight:.2f}')

    print('\n显示前4个喜用神:')
    for shishen, weight in sorted_xiyong[:4]:
        print(f'  {shishen}: {weight:.2f}')

    # 测试新的传统综合评分法
    print('\n=== 传统综合评分法详细分析 ===')
    strength, total_score, details = shishen_calc.calculate_body_strength_traditional(sizhu)
    print(f'综合得分: {total_score:.2f}')
    print(f'身强身弱: {strength}')
    print('\n各维度得分:')
    for dimension, detail in details.items():
        if isinstance(detail, dict):
            print(f'{dimension}: {detail["得分"]:.2f} (权重后: {detail["权重后得分"]:.2f})')
            if '详情' in detail:
                for item in detail['详情']:
                    print(f'  - {item}')
        else:
            print(f'{dimension}: {detail:.2f}')

if __name__ == "__main__":
    test_laijy()
