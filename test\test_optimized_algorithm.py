#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试优化后的流盘算法
"""

from main import BaziSystem

def test_optimized_algorithm():
    """测试优化后的流盘算法"""
    print("=" * 60)
    print("测试优化后的流盘算法")
    print("=" * 60)
    
    system = BaziSystem()
    
    user_info = {
        'name': '算法优化测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 格式化输出
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=False)
        
        # 保存到文件
        with open('算法优化测试_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('✅ 算法优化测试报告已生成：算法优化测试_八字分析_19991016_结果.txt')
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        liuyue_result = dayun_info.get('流月分析', {})
        liuri_result = dayun_info.get('流日分析', {})
        
        print("\n🎯 优化后的流盘计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"  总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"  吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        print(f"\n流月：{liuyue_result.get('流月', '未知')} ({liuyue_result.get('流月干支', '未知')})")
        print(f"  总得分：{liuyue_result.get('总得分', 0):.2f}")
        
        print(f"\n流日：{liuri_result.get('流日', '未知')} ({liuri_result.get('流日干支', '未知')})")
        print(f"  总得分：{liuri_result.get('总得分', 0):.2f}")
        
        # 检查权重配置是否生效
        print("\n📊 权重配置验证：")
        print("-" * 40)
        config = system.analyzer.config
        print(f"地支根气权重：{config['地支关系影响系数']['地支根气']}")
        print(f"天干比劫帮扶权重：{config['地支关系影响系数']['天干比劫帮扶']}")
        print(f"印星生扶权重：{config['地支关系影响系数']['印星生扶']}")
        
        if '流年喜用神加分' in config:
            print(f"天干喜用神加分：{config['流年喜用神加分']['天干喜用神加分']}")
            print(f"地支喜用神加分：{config['流年喜用神加分']['地支喜用神加分']}")
        
        # 分析优化效果
        print("\n🔍 优化效果分析：")
        print("-" * 40)
        
        # 检查是否还是负分
        liunian_score = liunian_result.get('总得分', 0)
        if liunian_score > 0:
            print(f"✅ 流年得分已转正：{liunian_score:.2f}分")
        elif liunian_score > -1:
            print(f"⚠️ 流年得分有所改善：{liunian_score:.2f}分（接近平衡）")
        else:
            print(f"❌ 流年得分仍为负：{liunian_score:.2f}分")
        
        # 检查身强身弱与流年的匹配度
        shishen_info = result.get('十神分析', {})
        body_analysis = shishen_info.get('身强身弱', {})
        if body_analysis.get('结果') == '身强':
            print("📋 身强格局分析：")
            print("  - 应该喜：克泄耗（官杀、财星、食伤）")
            print("  - 应该忌：生扶（印星、比劫）")
            print(f"  - 流年甲辰：甲(正财-喜)、辰(正印-忌)")
            print("  - 理论上应该得正分或接近平衡")
        
        print("\n" + "=" * 60)
        print("算法优化测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_optimized_algorithm()
