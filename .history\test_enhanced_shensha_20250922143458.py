#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试增强的神煞功能
"""

from main import BaziSystem

def test_enhanced_shensha():
    """测试增强的神煞功能"""
    print("=" * 60)
    print("测试增强的神煞功能")
    print("=" * 60)
    
    system = BaziSystem()
    
    # 测试数据：阳历1999年10月16日16:28分在重庆出生的女
    user_info = {
        'name': '神煞增强测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 生成分析报告
        result = system.run_analysis(user_info)
        
        # 提取排盘信息
        paipan_info = result.get('排盘信息', {})
        
        print("\n🎯 增强神煞测试结果：")
        print("-" * 40)
        
        # 显示各柱的神煞
        for pillar_name in ['年柱', '月柱', '日柱', '时柱']:
            if pillar_name in paipan_info:
                pillar_data = paipan_info[pillar_name]
                gan_zhi = f"{pillar_data['干支'][0]}{pillar_data['干支'][1]}"
                shensha = pillar_data.get('神煞', [])
                
                if shensha:
                    shensha_str = '、'.join(shensha)
                else:
                    shensha_str = '无'
                
                print(f"{pillar_name}({gan_zhi}): {shensha_str}")
        
        print("\n📊 新增神煞类型验证：")
        print("-" * 40)
        
        # 统计所有神煞
        all_shensha = set()
        for pillar_name in ['年柱', '月柱', '日柱', '时柱']:
            if pillar_name in paipan_info:
                shensha_list = paipan_info[pillar_name].get('神煞', [])
                all_shensha.update(shensha_list)
        
        # 检查新增的神煞类型
        new_shensha_types = ['天罗', '地网', '将军箭', '四废', '劫煞', '灾煞', '亡神', '元辰']
        found_new_shensha = []
        
        for shensha_type in new_shensha_types:
            if shensha_type in all_shensha:
                found_new_shensha.append(shensha_type)
        
        if found_new_shensha:
            print(f"✅ 发现新增神煞: {', '.join(found_new_shensha)}")
        else:
            print("⚠️ 未发现新增神煞类型")
        
        # 显示所有发现的神煞
        if all_shensha:
            print(f"\n🔍 本命盘所有神煞: {', '.join(sorted(all_shensha))}")
        else:
            print("\n❌ 未发现任何神煞")
        
        print("\n" + "=" * 60)
        print("神煞增强测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_enhanced_shensha()
