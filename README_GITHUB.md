# 八字占卜计算系统 (Bazi Fortune Calculation System)

一个基于Python的专业八字占卜计算系统，提供完整的八字分析、五行计算、十神分析、大运流年等功能。

## 功能特性

### 核心功能
- **阳历阴历转换**: 精确的公历农历转换，支持真太阳时计算
- **四柱八字排盘**: 自动计算年月日时四柱干支
- **五行力量分析**: 详细的五行力量计算，包含季节旺度、藏干权重、生扶克制
- **十神分析**: 完整的十神计算和身强身弱判断
- **喜用神分析**: 智能判断喜用神和忌神
- **大运流年**: 精确的大运起运时间和流年流月流日分析
- **排盘信息**: 详细的排盘格式，包含神煞、纳音、空亡等

### 高级功能
- **夏令时处理**: 自动处理中国历史夏令时期间的时间转换
- **真太阳时**: 基于地理位置的真太阳时修正
- **详细计算过程**: 提供完整的计算步骤和逻辑说明
- **多种输出格式**: 支持简单报告和详细分析报告

## 项目结构

```
calculation/
├── main.py                    # 主程序入口
├── calendar_converter.py     # 阳历阴历转换模块
├── wuxing_calculator.py      # 五行力量计算模块
├── shishen_calculator.py     # 十神分析模块
├── dayun_calculator.py       # 大运流年计算模块
├── paipan_calculator.py      # 排盘信息模块
├── comprehensive_result.py   # 综合结果输出模块
├── config.json              # 配置文件
├── city_coordinates.json    # 城市坐标数据
└── README.md               # 项目说明
```

## 安装依赖

```bash
pip install lunar-python
pip install zhdate
```

## 快速开始

### 基本使用

```python
from main import BaziSystem

# 创建系统实例
system = BaziSystem()

# 分析八字
result = system.analyze_bazi(
    birth_year=1990,
    birth_month=5,
    birth_day=15,
    birth_hour=14,
    birth_minute=30,
    city_name="广州",
    name="测试用户",
    gender="男"
)

print(result)
```

### 命令行使用

```bash
# 测试模式
python main.py --test

# 交互模式
python main.py --interactive

# API模式
python main.py --api
```

## 核心算法

### 五行力量计算
1. **基础分数统计**: 天干地支本气统计
2. **季节旺度调整**: 根据出生季节调整五行力量
3. **藏干权重计算**: 地支藏干按权重计入
4. **生扶克制计算**: 五行间相互生扶克制的影响
5. **最终力量汇总**: 综合计算各五行最终力量

### 十神分析
- 基于日干确定十神关系
- 天干地支藏干权重计算
- 身强身弱综合评分判断
- 喜用神忌神智能分析

### 大运流年
- 精确的起运时间计算
- 大运序列自动生成
- 流年流月流日详细分析
- 吉凶等级智能判定

## 技术特点

- **高精度计算**: 使用专业的农历库确保计算精度
- **完整算法**: 实现传统八字学完整的计算体系
- **详细过程**: 提供每一步计算的详细说明
- **灵活配置**: 通过配置文件调整各种参数
- **模块化设计**: 清晰的模块分工，便于维护和扩展

## 配置说明

系统通过 `config.json` 进行配置，主要包括：

- **五行旺度权重**: 季节对五行的影响系数
- **生扶克制系数**: 五行相互作用的影响程度
- **十神权重**: 不同位置十神的权重设置
- **吉凶等级判定**: 流年吉凶的判定标准
- **地支关系影响系数**: 地支间各种关系的影响系数

## 示例输出

系统会生成详细的八字分析报告，包括：

- 基本信息和时间转换详情
- 四柱八字排盘
- 五行力量详细计算过程
- 十神分析和身强身弱判断
- 喜用神忌神分析
- 大运流年详细计算
- 排盘信息和神煞分析

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
