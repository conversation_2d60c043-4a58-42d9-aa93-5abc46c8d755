"""
综合结果模块
汇总所有模块结果，提供统一的输出接口
"""

import json
from calendar_converter import CalendarConverter
from wuxing_calculator import WuxingCalculator
from shishen_calculator import <PERSON>shenCalculator
from dayun_calculator import DayunCalculator
from paipan_calculator import PaipanCalculator


class ComprehensiveAnalyzer:
    def __init__(self, config_path="config.json"):
        """初始化综合分析器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)

        # 初始化各个模块
        self.calendar_converter = CalendarConverter(config_path)
        self.wuxing_calculator = WuxingCalculator(config_path)
        self.shishen_calculator = ShishenCalculator(config_path)
        self.dayun_calculator = DayunCalculator(config_path)
        self.paipan_calculator = PaipanCalculator()
    
    def analyze_complete_bazi(self, birth_year, birth_month, birth_day,
                             birth_hour, birth_minute, city_name, name, gender='男'):
        """完整的八字分析"""

        # 1. 阳历阴历换算
        print("正在进行阳历阴历换算...")
        calendar_result = self.calendar_converter.process_birth_info(
            birth_year, birth_month, birth_day, birth_hour, birth_minute, city_name, name
        )

        if calendar_result is None:
            return {"错误": "阳历阴历换算失败"}

        # 2. 五行计算
        print("正在计算五行力量...")
        # 只使用详细计算过程，不再重复计算
        season = self.wuxing_calculator.get_season_from_month(calendar_result['四柱']['月柱'][1])
        wuxing_details = self._get_wuxing_calculation_details(calendar_result['四柱'], season)

        # 从详细计算中提取最终的五行力量分布
        wuxing_scores = self._extract_wuxing_scores_from_details(wuxing_details)

        # 3. 十神计算
        print("正在分析十神...")
        shishen_weights = self.shishen_calculator.calculate_shishen_weights(calendar_result['四柱'])
        body_analysis = self.shishen_calculator.analyze_body_strength(calendar_result['四柱'])
        xiyong_info = self.shishen_calculator.determine_xiyongshen(body_analysis['strength'], shishen_weights)
        spouse_info = self.shishen_calculator.analyze_spouse_info(calendar_result['四柱'], gender)

        # 获取十神计算的详细过程
        shishen_details = self._get_shishen_calculation_details(calendar_result['四柱'], shishen_weights)

        # 4. 排盘补充信息计算
        print("正在计算排盘信息...")
        paipan_info = self.paipan_calculator.calculate_paipan_info(
            calendar_result['四柱'],
            {'十神权重': shishen_weights, '身强身弱': body_analysis}
        )

        # 4. 流年大运计算
        print("正在分析大运流年...")
        qiyun_info = self.dayun_calculator.calculate_qiyun_time(calendar_result, gender)
        dayun_list = self.dayun_calculator.generate_dayun_sequence(
            calendar_result['四柱']['月柱'], qiyun_info['起运方向']
        )
        dayun_ages = self.dayun_calculator.calculate_dayun_ages(qiyun_info)

        # 获取大运计算的详细过程
        dayun_details = self._get_dayun_calculation_details(calendar_result, gender, qiyun_info)

        # 流年流月流日分析 - 使用dayun_calculator的完整方法
        target_date = self.config['流年流月流日分析日期']

        # 使用dayun_calculator的方法进行流年流月流日分析
        liunian_result = self.dayun_calculator.analyze_liunian(calendar_result, dayun_list[0], xiyong_info, target_date)
        liuyue_result = self.dayun_calculator.analyze_liuyue(liunian_result, target_date, calendar_result, xiyong_info)
        liuri_result = self.dayun_calculator.analyze_liuri(liunian_result, liuyue_result, target_date, calendar_result, xiyong_info)

        # 简化详细过程
        liunian_details = {
            "计算说明": "使用dayun_calculator模块的简化算法",
            "流年干支": liunian_result['流年干支'],
            "总得分": liunian_result['总得分'],
            "吉凶等级": liunian_result['吉凶等级']
        }
        liuyue_details = {
            "计算说明": "基于流年得分的30%",
            "流月干支": liuyue_result['流月干支'],
            "总得分": liuyue_result['总得分']
        }
        liuri_details = {
            "计算说明": "基于流年和流月得分的10%",
            "流日干支": liuri_result['流日干支'],
            "总得分": liuri_result['总得分']
        }

        updated_liunian_result = liunian_result
        updated_liuyue_result = liuyue_result
        updated_liuri_result = liuri_result

        # 5. 合婚计算（预留接口）
        marriage_result = self.analyze_marriage_compatibility()

        # 汇总结果
        comprehensive_result = {
            "基本信息": calendar_result,
            "五行分析": {
                "五行力量": wuxing_scores,
                "当前季节": season,
                "计算详情": wuxing_details
            },
            "十神分析": {
                "十神权重": shishen_weights,
                "身强身弱": body_analysis,
                "喜用神忌神": xiyong_info,
                "夫妻宫配偶星": spouse_info,
                "计算详情": shishen_details
            },
            "排盘信息": paipan_info,
            "大运流年": {
                "起运信息": qiyun_info,
                "大运序列": [{"大运": dayun, "年龄范围": f"{start_age}-{end_age}岁"}
                           for dayun, (start_age, end_age) in zip(dayun_list, dayun_ages)],
                "流年分析": updated_liunian_result,
                "流月分析": updated_liuyue_result,
                "流日分析": updated_liuri_result,
                "计算详情": dayun_details,
                "流年计算详情": liunian_details,
                "流月计算详情": liuyue_details,
                "流日计算详情": liuri_details
            },
            "合婚分析": marriage_result
        }

        return comprehensive_result

    def _get_wuxing_calculation_details(self, sizhu, season):
        """获取五行计算的详细过程"""
        details = {
            "天干五行统计": {},
            "地支本气统计": {},
            "地支藏干详情": {},
            "季节旺度影响": {},
            "生扶克制计算": {},
            "最终力量计算": {}
        }

        # 天干五行统计
        tiangan_wuxing = {}
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]),
                        ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
            if wuxing not in tiangan_wuxing:
                tiangan_wuxing[wuxing] = []
            tiangan_wuxing[wuxing].append(f"{pos}({gan})")
        details["天干五行统计"] = tiangan_wuxing

        # 地支本气统计
        dizhi_wuxing = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            wuxing = self.wuxing_calculator.dizhi_info[zhi]['五行']
            if wuxing not in dizhi_wuxing:
                dizhi_wuxing[wuxing] = []
            dizhi_wuxing[wuxing].append(f"{pos}({zhi})")
        details["地支本气统计"] = dizhi_wuxing

        # 地支藏干详情
        canggan_details = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.wuxing_calculator.dizhi_info[zhi]['藏干']
            canggan_info = []
            for gan, weight in canggan_dict.items():
                wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
                canggan_info.append(f"{gan}({wuxing}) 权重{weight}")
            canggan_details[f"{pos}({zhi})"] = canggan_info
        details["地支藏干详情"] = canggan_details

        # 季节旺度影响
        season_effects = self.wuxing_calculator.season_wangdu[season]
        details["季节旺度影响"] = {
            "当前季节": season,
            "旺": f"{season_effects['旺']} (得令权重: {self.config['五行计算参数']['得令权重']})",
            "死": f"{season_effects['死']} (失令权重: {self.config['五行计算参数']['失令权重']})",
            "其他": f"{season_effects['相']}, {season_effects['休']}, {season_effects['囚']} (平和)"
        }

        # 详细的五行力量计算过程
        wuxing_detailed_calc = self._calculate_wuxing_detailed_steps(sizhu, season)
        details["五行力量详细计算"] = wuxing_detailed_calc

        return details

    def _get_shishen_calculation_details(self, sizhu, shishen_weights):
        """获取十神计算的详细过程"""
        day_gan = sizhu['日柱'][0]
        details = {
            "日主信息": {},
            "天干十神分析": {},
            "地支藏干十神分析": {},
            "十神权重汇总": {}
        }

        # 日主信息
        day_info = self.shishen_calculator.tiangan_info[day_gan]
        details["日主信息"] = {
            "日干": day_gan,
            "五行": day_info['五行'],
            "阴阳": day_info['阴阳']
        }

        # 天干十神分析
        tiangan_shishen = {}
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('时干', sizhu['时柱'][0])]:
            if gan != day_gan:
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                tiangan_shishen[f"{pos}({gan})"] = f"{shishen} (权重: 1.0)"
        details["天干十神分析"] = tiangan_shishen

        # 地支藏干十神分析
        dizhi_shishen = {}
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.shishen_calculator.dizhi_canggan[zhi]
            canggan_analysis = []
            for canggan, weight in canggan_dict.items():
                if canggan != day_gan:
                    shishen = self.shishen_calculator.determine_shishen(day_gan, canggan)
                    canggan_analysis.append(f"{canggan}→{shishen} (权重: {weight})")
            if canggan_analysis:
                dizhi_shishen[f"{pos}({zhi})"] = canggan_analysis
        details["地支藏干十神分析"] = dizhi_shishen

        # 十神权重汇总
        details["十神权重汇总"] = {k: f"{v:.2f}" for k, v in shishen_weights.items()}

        return details

    def _get_dayun_calculation_details(self, birth_info, gender, qiyun_info):
        """获取大运计算的详细过程"""
        year_gan = birth_info['四柱']['年柱'][0]

        details = {
            "起运规则判断": {},
            "节气距离计算": {},
            "起运时间换算": {},
            "大运排列规则": {}
        }

        # 起运规则判断
        yang_gan = ['甲', '丙', '戊', '庚', '壬']
        is_yang_gan = year_gan in yang_gan
        gan_type = "阳干" if is_yang_gan else "阴干"

        if (is_yang_gan and gender == '男') or (not is_yang_gan and gender == '女'):
            rule = "阳男阴女顺排"
            direction = "顺排"
        else:
            rule = "阴男阳女逆排"
            direction = "逆排"

        details["起运规则判断"] = {
            "年干": f"{year_gan} ({gan_type})",
            "性别": gender,
            "适用规则": rule,
            "排列方向": direction
        }

        # 节气距离计算
        details["节气距离计算"] = {
            "距离节气天数": f"{qiyun_info['距离节气天数']}天",
            "计算方法": "基于月份节气大致日期的简化计算"
        }

        # 起运时间换算
        days = qiyun_info['距离节气天数']
        years = int(days // 3)
        remaining_days = days % 3
        months = int(remaining_days * 4)

        details["起运时间换算"] = {
            "换算公式": "3天 = 1年, 1天 = 4个月",
            "计算过程": f"{days}天 ÷ 3 = {years}年 余 {remaining_days:.2f}天",
            "月份计算": f"{remaining_days:.2f}天 × 4 = {months}个月",
            "最终结果": f"{years}岁{months}个月"
        }

        # 大运排列规则
        month_pillar = birth_info['四柱']['月柱']
        details["大运排列规则"] = {
            "起始月柱": month_pillar,
            "排列方向": direction,
            "每步大运": "10年"
        }

        return details

    def _analyze_liunian_detailed(self, birth_info, dayun, xiyong_info, target_date):
        """详细分析流年 - 使用config参数的完整算法"""
        target_year = target_date['年']

        # 获取流年干支
        from lunar_python import Solar
        solar = Solar.fromYmd(target_year, 1, 1)
        lunar = solar.getLunar()
        liunian_ganzhi = lunar.getYearInGanZhi()

        liunian_gan, liunian_zhi = liunian_ganzhi[0], liunian_ganzhi[1]

        # 使用comprehensive_result中的详细计算逻辑
        liunian_details = self._calculate_liunian_detailed_steps(None, xiyong_info, target_date, birth_info)

        # 从详细计算中提取总得分
        total_score = 0
        if '第三步_流年综合得分计算' in liunian_details:
            total_score = liunian_details['第三步_流年综合得分计算'].get('总得分', 0)

        # 判断吉凶等级
        if total_score >= self.config['吉凶等级判定']['大吉']:
            level = '大吉'
            desc = '诸事顺遂'
        elif total_score >= self.config['吉凶等级判定']['吉']:
            level = '吉'
            desc = '小有收获'
        elif total_score > self.config['吉凶等级判定']['凶']:
            level = '平'
            desc = '平淡无奇'
        elif total_score >= self.config['吉凶等级判定']['大凶']:
            level = '凶'
            desc = '阻力频生'
        else:
            level = '大凶'
            desc = '动荡不安'

        return {
            '流年': f"{target_year}年",
            '流年干支': liunian_ganzhi,
            '总得分': round(total_score, 2),
            '吉凶等级': level,
            '运势解读': desc
        }

    def _analyze_liuyue_detailed(self, liunian_result, xiyong_info, target_date, birth_info, dayun):
        """详细分析流月"""
        # 使用comprehensive_result中的详细计算逻辑
        liuyue_details = self._get_liuyue_calculation_details(None, xiyong_info, target_date, birth_info, dayun, liunian_result)

        # 从详细计算中提取总得分
        total_score = 0
        if '第三步_流月综合得分计算' in liuyue_details:
            total_score = liuyue_details['第三步_流月综合得分计算'].get('总得分', 0)

        target_month = target_date['月']
        from lunar_python import Solar
        solar = Solar.fromYmd(target_date['年'], target_month, 1)
        lunar = solar.getLunar()
        liuyue_ganzhi = lunar.getMonthInGanZhi()

        return {
            '流月': f"{target_month}月",
            '流月干支': liuyue_ganzhi,
            '总得分': round(total_score, 2),
            '基于流年': liunian_result['流年']
        }

    def _analyze_liuri_detailed(self, liunian_result, liuyue_result, xiyong_info, target_date, birth_info, dayun):
        """详细分析流日"""
        # 使用comprehensive_result中的详细计算逻辑
        liuri_details = self._get_liuri_calculation_details(None, xiyong_info, target_date, birth_info, dayun, liunian_result, liuyue_result)

        # 从详细计算中提取总得分
        total_score = 0
        if '第三步_流日综合得分计算' in liuri_details:
            total_score = liuri_details['第三步_流日综合得分计算'].get('总得分', 0)

        from lunar_python import Solar
        solar = Solar.fromYmd(target_date['年'], target_date['月'], target_date['日'])
        lunar = solar.getLunar()
        liuri_ganzhi = lunar.getDayInGanZhi()

        return {
            '流日': f"{target_date['月']}月{target_date['日']}日",
            '流日干支': liuri_ganzhi,
            '总得分': round(total_score, 2),
            '基于流年流月': f"{liunian_result['流年']}{liuyue_result['流月']}"
        }

    def _get_liunian_calculation_details(self, liunian_result, xiyong_info, target_date, birth_info):
        """获取流年流月流日计算的详细过程"""
        # 获取详细的计算步骤
        details, _ = self._calculate_liunian_detailed_steps(liunian_result, xiyong_info, target_date, birth_info)
        return details

    def _calculate_liunian_detailed_steps(self, liunian_result, xiyong_info, target_date, birth_info):
        """计算流年的详细步骤，展示每一步的公式和数值"""
        from lunar_python import Solar, Lunar

        details = {
            "第一步_流年干支获取": {},
            "第二步_原局四柱分析": {},
            "第三步_喜忌神权重计算": {},
            "第四步_流年与原局作用关系": {},
            "第五步_综合得分计算": {},
            "第六步_吉凶等级判定": {}
        }

        # 第一步：流年干支获取
        target_year = target_date['年']
        solar = Solar.fromYmd(target_year, 1, 1)
        lunar = solar.getLunar()
        liunian_ganzhi = lunar.getYearInGanZhi()
        liunian_gan, liunian_zhi = liunian_ganzhi[0], liunian_ganzhi[1]

        details["第一步_流年干支获取"] = {
            "计算目标": f"获取{target_year}年的干支",
            "使用工具": "lunar_python库",
            "计算过程": f"Solar.fromYmd({target_year}, 1, 1) -> getLunar() -> getYearInGanZhi()",
            "计算结果": f"{target_year}年 = {liunian_ganzhi} (天干:{liunian_gan}, 地支:{liunian_zhi})"
        }

        # 第二步：原局四柱分析
        sizhu = birth_info['四柱']
        tiangan_info = self.shishen_calculator.tiangan_info

        yuanju_analysis = {
            "年柱": f"{sizhu['年柱']} (天干:{sizhu['年柱'][0]}={tiangan_info[sizhu['年柱'][0]]['五行']}{tiangan_info[sizhu['年柱'][0]]['阴阳']}, 地支:{sizhu['年柱'][1]})",
            "月柱": f"{sizhu['月柱']} (天干:{sizhu['月柱'][0]}={tiangan_info[sizhu['月柱'][0]]['五行']}{tiangan_info[sizhu['月柱'][0]]['阴阳']}, 地支:{sizhu['月柱'][1]})",
            "日柱": f"{sizhu['日柱']} (天干:{sizhu['日柱'][0]}={tiangan_info[sizhu['日柱'][0]]['五行']}{tiangan_info[sizhu['日柱'][0]]['阴阳']}, 地支:{sizhu['日柱'][1]})",
            "时柱": f"{sizhu['时柱']} (天干:{sizhu['时柱'][0]}={tiangan_info[sizhu['时柱'][0]]['五行']}{tiangan_info[sizhu['时柱'][0]]['阴阳']}, 地支:{sizhu['时柱'][1]})"
        }

        details["第二步_原局四柱分析"] = {
            "日主": f"{sizhu['日柱'][0]} ({tiangan_info[sizhu['日柱'][0]]['五行']}{tiangan_info[sizhu['日柱'][0]]['阴阳']})",
            "原局四柱": yuanju_analysis
        }

        # 第三步：喜忌神权重计算
        xiyong_list, ji_list = xiyong_info

        xiyong_details = []
        for shishen, weight in xiyong_list[:4]:
            xiyong_details.append(f"{shishen}(权重:{weight:.2f})")

        ji_details = []
        for shishen, weight in ji_list[:4]:
            ji_details.append(f"{shishen}(权重:{weight:.2f})")

        details["第三步_喜忌神权重计算"] = {
            "计算依据": "基于身强身弱判断和十神权重分析",
            "喜用神": xiyong_details,
            "忌神": ji_details,
            "权重说明": "权重越高，对命主影响越大"
        }

        # 第四步：流年与原局作用关系（详细计算每个作用）
        day_gan = sizhu['日柱'][0]

        # 流年天干与原局天干的作用（包括日干）
        tiangan_zuoyong = []
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            # 计算天干关系（包含天干五合处理）
            relation_result = self._calculate_tiangan_relation(liunian_gan, gan, birth_info, None, liunian_ganzhi)

            if len(relation_result) == 3:  # 天干五合情况
                relation, huashen_or_coefficient, is_hehua_success = relation_result

                # 计算十神关系
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                gan_base_score, gan_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                # 流年天干的十神关系（与日干比较）
                liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
                liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

                if huashen_or_coefficient:  # 合化成功
                    final_score, calculation_detail = self._calculate_wuhe_score(
                        liunian_gan, gan, liunian_base_score, gan_base_score,
                        huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
                    )

                    tiangan_zuoyong.append({
                        "原局": f"{pos}({gan})",
                        "五行关系": relation,
                        "十神": shishen,
                        "喜忌": gan_xiji_type,
                        "计算详情": calculation_detail,
                        "计算公式": f"详细计算: {calculation_detail['分数变化']} → {calculation_detail['总分']}",
                        "详细过程": calculation_detail,
                        "得分": final_score
                    })
                else:  # 合绊
                    final_score, calculation_detail = self._calculate_wuhe_score(
                        liunian_gan, gan, liunian_base_score, gan_base_score,
                        None, is_hehua_success, xiyong_list, ji_list, day_gan
                    )

                    tiangan_zuoyong.append({
                        "原局": f"{pos}({gan})",
                        "五行关系": relation,
                        "十神": shishen,
                        "喜忌": gan_xiji_type,
                        "计算详情": calculation_detail,
                        "计算公式": f"详细计算: {calculation_detail['合绊公式']}",
                        "详细过程": calculation_detail,
                        "得分": final_score
                    })
            else:  # 普通生克关系
                relation, coefficient = relation_result[0], relation_result[1]

                # 计算对象天干的十神关系和基准分
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                # 普通天干关系：对象基准分 × 关系系数
                final_score = base_score * coefficient

                liunian_wuxing = tiangan_info[liunian_gan]['五行']
                gan_wuxing = tiangan_info[gan]['五行']

                tiangan_zuoyong.append({
                    "原局": f"{pos}({gan})",
                    "五行关系": f"{liunian_gan}({liunian_wuxing}) vs {gan}({gan_wuxing}) = {relation}",
                    "十神": shishen,
                    "喜忌": xiji_type,
                    "计算公式": f"{base_score} × {coefficient} = {final_score:.2f}",
                    "得分": final_score
                })

        # 流年天干与大运干的作用
        # 这里需要获取当前大运信息，暂时使用示例
        dayun_gan = "壬"  # 从大运信息中获取
        dayun_zhi = "午"  # 从大运信息中获取

        # 计算流年天干与大运天干的关系
        dayun_relation_result = self._calculate_tiangan_relation(liunian_gan, dayun_gan, birth_info, (dayun_gan, dayun_zhi), liunian_ganzhi)

        if len(dayun_relation_result) == 3:  # 天干五合情况
            dayun_relation, huashen_or_coefficient, is_hehua_success = dayun_relation_result

            # 大运天干的十神关系
            dayun_shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(dayun_shishen, xiyong_list, ji_list)

            # 流年天干的十神关系
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

            dayun_final_score, calculation_detail = self._calculate_wuhe_score(
                liunian_gan, dayun_gan, liunian_base_score, dayun_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            tiangan_zuoyong.append({
                "原局": f"大运干({dayun_gan})",
                "五行关系": dayun_relation,
                "十神": dayun_shishen,
                "喜忌": dayun_xiji_type,
                "计算详情": calculation_detail,
                "计算公式": calculation_detail["总分"],
                "得分": dayun_final_score
            })
        else:  # 普通生克关系
            dayun_relation, dayun_coefficient = dayun_relation_result[0], dayun_relation_result[1]

            # 大运天干的十神关系和基准分
            dayun_shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(dayun_shishen, xiyong_list, ji_list)

            # 普通天干关系：对象基准分 × 关系系数
            dayun_final_score = dayun_base_score * dayun_coefficient

            dayun_gan_wuxing = tiangan_info[dayun_gan]['五行']
            liunian_wuxing = tiangan_info[liunian_gan]['五行']

            tiangan_zuoyong.append({
                "原局": f"大运干({dayun_gan})",
                "五行关系": f"{liunian_gan}({liunian_wuxing}) vs {dayun_gan}({dayun_gan_wuxing}) = {dayun_relation}",
                "十神": dayun_shishen,
                "喜忌": dayun_xiji_type,
                "计算公式": f"{dayun_base_score} × {dayun_coefficient} = {dayun_final_score:.2f}",
                "得分": dayun_final_score
            })

        # 流年地支与原局地支的作用
        dizhi_zuoyong = []
        dizhi_info = self.wuxing_calculator.dizhi_info

        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]), ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            relation_type, coefficient = self._calculate_dizhi_relation(liunian_zhi, zhi, birth_info)

            # 地支的五行属性
            liunian_zhi_wuxing = dizhi_info[liunian_zhi]['五行']
            zhi_wuxing = dizhi_info[zhi]['五行']

            # 地支的十神关系（通过本气）
            zhi_bengqi = list(dizhi_info[zhi]['藏干'].keys())[0]  # 第一个藏干是本气
            zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, zhi_bengqi)

            # 判断是喜是忌
            is_zhi_xiyong = any(zhi_shishen == item[0] for item in xiyong_list)
            is_zhi_ji = any(zhi_shishen == item[0] for item in ji_list)

            if is_zhi_xiyong:
                zhi_xiji_type = "喜用神"
                zhi_base_score = 1.0
            elif is_zhi_ji:
                zhi_xiji_type = "忌神"
                zhi_base_score = -1.0
            else:
                zhi_xiji_type = "中性"
                zhi_base_score = 0.0

            zhi_final_score = zhi_base_score * coefficient

            dizhi_zuoyong.append({
                "原局": f"{pos}({zhi})",
                "五行关系": f"{liunian_zhi}({liunian_zhi_wuxing}) vs {zhi}({zhi_wuxing}) = {relation_type}",
                "十神": zhi_shishen,
                "喜忌": zhi_xiji_type,
                "计算公式": f"{zhi_base_score} × {coefficient} = {zhi_final_score:.2f}",
                "得分": zhi_final_score
            })

        # 流年地支与大运地支的作用
        dayun_relation_type, dayun_zhi_coefficient = self._calculate_dizhi_relation(liunian_zhi, dayun_zhi, birth_info)

        dayun_zhi_wuxing = dizhi_info[dayun_zhi]['五行']
        dayun_zhi_bengqi = list(dizhi_info[dayun_zhi]['藏干'].keys())[0]
        dayun_zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_zhi_bengqi)

        is_dayun_zhi_xiyong = any(dayun_zhi_shishen == item[0] for item in xiyong_list)
        is_dayun_zhi_ji = any(dayun_zhi_shishen == item[0] for item in ji_list)

        if is_dayun_zhi_xiyong:
            dayun_zhi_xiji_type = "喜用神"
            dayun_zhi_base_score = 1.0
        elif is_dayun_zhi_ji:
            dayun_zhi_xiji_type = "忌神"
            dayun_zhi_base_score = -1.0
        else:
            dayun_zhi_xiji_type = "中性"
            dayun_zhi_base_score = 0.0

        dayun_zhi_final_score = dayun_zhi_base_score * dayun_zhi_coefficient

        dizhi_zuoyong.append({
            "原局": f"大运支({dayun_zhi})",
            "五行关系": f"{liunian_zhi}({liunian_zhi_wuxing}) vs {dayun_zhi}({dayun_zhi_wuxing}) = {dayun_relation_type}",
            "十神": dayun_zhi_shishen,
            "喜忌": dayun_zhi_xiji_type,
            "计算公式": f"{dayun_zhi_base_score} × {dayun_zhi_coefficient} = {dayun_zhi_final_score:.2f}",
            "得分": dayun_zhi_final_score
        })

        details["第四步_流年与原局作用关系"] = {
            "流年干支": f"{liunian_gan}{liunian_zhi}",
            "天干作用详情": tiangan_zuoyong,
            "地支作用详情": dizhi_zuoyong
        }

        # 添加流年天干自身得分
        liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
        liunian_self_score, liunian_self_xiji = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

        tiangan_zuoyong.append({
            "原局": f"流年干自身({liunian_gan})",
            "五行关系": "流年天干自身",
            "十神": liunian_shishen,
            "喜忌": liunian_self_xiji,
            "计算公式": f"{liunian_self_score} × 1.0 = {liunian_self_score:.2f}",
            "得分": liunian_self_score
        })

        # 添加流年地支自身得分
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        liunian_zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)
        liunian_zhi_self_score, liunian_zhi_self_xiji = self._get_xiji_score(liunian_zhi_shishen, xiyong_list, ji_list)

        dizhi_zuoyong.append({
            "原局": f"流年支自身({liunian_zhi})",
            "五行关系": "流年地支自身",
            "十神": liunian_zhi_shishen,
            "喜忌": liunian_zhi_self_xiji,
            "计算公式": f"{liunian_zhi_self_score} × 1.0 = {liunian_zhi_self_score:.2f}",
            "得分": liunian_zhi_self_score
        })

        # 第五步：综合得分计算
        tiangan_total = sum(item["得分"] for item in tiangan_zuoyong)
        dizhi_total = sum(item["得分"] for item in dizhi_zuoyong)

        # 添加流年喜用神加分机制
        xiyong_list, ji_list = xiyong_info
        liunian_gan_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        liunian_zhi_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)

        # 检查流年天干是否为喜用神
        liunian_gan_is_xiyong = any(liunian_gan_shishen == shishen for shishen, _ in xiyong_list)
        liunian_zhi_is_xiyong = any(liunian_zhi_shishen == shishen for shishen, _ in xiyong_list)

        # 计算喜用神加分
        xiyong_bonus = 0
        xiyong_details = []

        if 'flow年喜用神加分' in self.config:
            config_bonus = self.config['flow年喜用神加分']
        else:
            config_bonus = self.config.get('流年喜用神加分', {
                "天干喜用神加分": 0.5,
                "地支喜用神加分": 0.3,
                "干支都喜用神加分": 1.0
            })

        if liunian_gan_is_xiyong and liunian_zhi_is_xiyong:
            xiyong_bonus = config_bonus.get("干支都喜用神加分", 1.0)
            xiyong_details.append(f"干支都为喜用神: +{xiyong_bonus:.2f}")
        else:
            if liunian_gan_is_xiyong:
                gan_bonus = config_bonus.get("天干喜用神加分", 0.5)
                xiyong_bonus += gan_bonus
                xiyong_details.append(f"天干为喜用神: +{gan_bonus:.2f}")
            if liunian_zhi_is_xiyong:
                zhi_bonus = config_bonus.get("地支喜用神加分", 0.3)
                xiyong_bonus += zhi_bonus
                xiyong_details.append(f"地支为喜用神: +{zhi_bonus:.2f}")

        total_score = tiangan_total + dizhi_total + xiyong_bonus

        details["第五步_综合得分计算"] = {
            "计算公式": "∑(天干作用得分) + ∑(地支作用得分) + 喜用神加分",
            "天干得分详细": " + ".join([f"({item['得分']:.2f})" for item in tiangan_zuoyong]),
            "天干小计": f"{tiangan_total:.2f}",
            "地支得分详细": " + ".join([f"({item['得分']:.2f})" for item in dizhi_zuoyong]),
            "地支小计": f"{dizhi_total:.2f}",
            "喜用神加分详细": " + ".join(xiyong_details) if xiyong_details else "无",
            "喜用神加分": f"{xiyong_bonus:.2f}",
            "总得分": f"{total_score:.2f}"
        }

        # 第六步：吉凶等级判定
        config_levels = self.config['吉凶等级判定']

        if total_score >= config_levels['大吉']:
            level = '大吉'
            desc = '诸事顺遂'
        elif total_score >= config_levels['吉']:
            level = '吉'
            desc = '小有收获'
        elif total_score > config_levels['凶']:
            level = '平'
            desc = '平淡无奇'
        elif total_score >= config_levels['大凶']:
            level = '凶'
            desc = '阻力频生'
        else:
            level = '大凶'
            desc = '动荡不安'

        details["第六步_吉凶等级判定"] = {
            "判定标准": {
                "大吉": f"≥ {config_levels['大吉']}分",
                "吉": f"≥ {config_levels['吉']}分",
                "平": f"> {config_levels['凶']}分",
                "凶": f"≥ {config_levels['大凶']}分",
                "大凶": f"< {config_levels['大凶']}分"
            },
            "实际得分": total_score,
            "判定结果": level,
            "运势解读": desc
        }

        # 更新流年结果，使用我们详细计算的数据
        updated_liunian_result = {
            "流年": f"{target_year}年",
            "流年干支": liunian_ganzhi,
            "总得分": total_score,
            "吉凶等级": level,
            "运势解读": desc
        }

        return details, updated_liunian_result

    def _calculate_wuxing_detailed_steps(self, sizhu, season):
        """计算五行的详细步骤，展示每一步的公式和数值"""
        wuxing_steps = {
            "第一步_基础分数统计": {},
            "第二步_季节旺度调整": {},
            "第三步_藏干权重计算": {},
            "第四步_生扶克制计算": {},
            "第五步_最终力量汇总": {}
        }

        # 第一步：基础分数统计
        base_counts = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}
        tiangan_detail = []

        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]),
                        ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
            base_counts[wuxing] += 1
            tiangan_detail.append(f"{pos}({gan}) = {wuxing} +1")

        dizhi_detail = []
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            wuxing = self.wuxing_calculator.dizhi_info[zhi]['五行']
            base_counts[wuxing] += 1
            dizhi_detail.append(f"{pos}({zhi}) = {wuxing} +1")

        wuxing_steps["第一步_基础分数统计"] = {
            "天干统计": tiangan_detail,
            "地支本气统计": dizhi_detail,
            "基础分数": {k: f"{v}分" for k, v in base_counts.items()}
        }

        # 第二步：季节旺度调整
        season_effects = self.wuxing_calculator.season_wangdu[season]
        deling_weight = self.config['五行计算参数']['得令权重']
        shiling_weight = self.config['五行计算参数']['失令权重']

        season_adjusted = {}
        season_detail = []

        for wuxing, count in base_counts.items():
            if wuxing == season_effects['旺']:
                adjusted = count * deling_weight
                season_detail.append(f"{wuxing}: {count} × {deling_weight}(得令) = {adjusted}")
            elif wuxing == season_effects['死']:
                adjusted = count * shiling_weight
                season_detail.append(f"{wuxing}: {count} × {shiling_weight}(失令) = {adjusted}")
            else:
                adjusted = count * 1.0
                season_detail.append(f"{wuxing}: {count} × 1.0(平和) = {adjusted}")
            season_adjusted[wuxing] = adjusted

        wuxing_steps["第二步_季节旺度调整"] = {
            "调整公式": "基础分数 × 季节权重",
            "详细计算": season_detail,
            "调整后分数": {k: f"{v:.2f}分" for k, v in season_adjusted.items()}
        }

        # 第三步：藏干权重计算
        canggan_detail = []
        canggan_scores = {"木": 0, "火": 0, "土": 0, "金": 0, "水": 0}

        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            canggan_dict = self.wuxing_calculator.dizhi_info[zhi]['藏干']
            zhi_detail = []

            for gan, weight in canggan_dict.items():
                if gan != list(canggan_dict.keys())[0]:  # 跳过本气（已在第一步计算）
                    wuxing = self.wuxing_calculator.tiangan_info[gan]['五行']
                    score = weight
                    canggan_scores[wuxing] += score
                    zhi_detail.append(f"{gan}({wuxing}) × {weight} = {score}")

            if zhi_detail:
                canggan_detail.append(f"{pos}({zhi}): {', '.join(zhi_detail)}")

        wuxing_steps["第三步_藏干权重计算"] = {
            "计算说明": "地支藏干按权重计入对应五行",
            "详细计算": canggan_detail,
            "藏干得分": {k: f"{v:.2f}分" for k, v in canggan_scores.items()}
        }

        # 第四步：生扶克制计算
        # 合并季节调整分数和藏干分数
        combined_scores = {}
        for wuxing in ["木", "火", "土", "金", "水"]:
            combined_scores[wuxing] = season_adjusted[wuxing] + canggan_scores[wuxing]

        # 计算生扶克制
        shengfu_detail = []
        kezhi_detail = []
        final_scores = combined_scores.copy()

        shengfu_coeff = self.config['五行计算参数']['生扶系数']
        kezhi_coeff = self.config['五行计算参数']['克制系数']

        for wuxing in ["木", "火", "土", "金", "水"]:
            # 计算生我的力量
            sheng_wo = self.wuxing_calculator.wuxing_sheng_reverse.get(wuxing, "")
            if sheng_wo and sheng_wo in combined_scores:
                sheng_power = combined_scores[sheng_wo] * shengfu_coeff
                final_scores[wuxing] += sheng_power
                shengfu_detail.append(f"{wuxing} 得 {sheng_wo} 生扶: {combined_scores[sheng_wo]:.2f} × {shengfu_coeff} = +{sheng_power:.2f}")

            # 计算克我的力量
            ke_wo = self.wuxing_calculator.wuxing_ke_reverse.get(wuxing, "")
            if ke_wo and ke_wo in combined_scores:
                ke_power = combined_scores[ke_wo] * kezhi_coeff
                final_scores[wuxing] -= ke_power
                kezhi_detail.append(f"{wuxing} 被 {ke_wo} 克制: {combined_scores[ke_wo]:.2f} × {kezhi_coeff} = -{ke_power:.2f}")

        wuxing_steps["第四步_生扶克制计算"] = {
            "基础分数": {k: f"{v:.2f}分" for k, v in combined_scores.items()},
            "生扶计算": shengfu_detail,
            "克制计算": kezhi_detail,
            "生扶系数": shengfu_coeff,
            "克制系数": kezhi_coeff
        }

        # 第五步：最终力量汇总
        total_power = sum(final_scores.values())
        percentages = {k: (v/total_power)*100 if total_power > 0 else 0 for k, v in final_scores.items()}

        # 判断五行状态 - 使用config配置的阈值
        status_thresholds = self.config.get('五行状态判断阈值', {
            '过强': 4.0,
            '中和偏强': 2.5,
            '中和': 1.5,
            '偏弱': 0.5
        })

        status_detail = []
        for wuxing, score in final_scores.items():
            if score >= status_thresholds['过强']:
                status = "过强"
            elif score >= status_thresholds['中和偏强']:
                status = "中和偏强"
            elif score >= status_thresholds['中和']:
                status = "中和"
            elif score >= status_thresholds['偏弱']:
                status = "偏弱"
            else:
                status = "很弱"

            status_detail.append(f"{wuxing}: {score:.2f}分 ({percentages[wuxing]:.1f}%) = {status}")

        wuxing_steps["第五步_最终力量汇总"] = {
            "最终得分": {k: f"{v:.2f}分" for k, v in final_scores.items()},
            "总力量": f"{total_power:.2f}分",
            "力量占比": {k: f"{v:.1f}%" for k, v in percentages.items()},
            "状态判定": status_detail
        }

        return wuxing_steps



    def _extract_wuxing_scores_from_details(self, wuxing_details):
        """从详细计算中提取五行力量分布"""
        try:
            # 直接从详细计算的第五步中提取数据
            step5_data = wuxing_details['五行力量详细计算']['第五步_最终力量汇总']
            final_scores = step5_data['最终得分']
            status_judgments = step5_data['状态判定']

            # 构建与原来格式兼容的数据结构
            wuxing_scores = {}
            for wuxing in ['木', '火', '土', '金', '水']:
                try:
                    # 提取分数
                    score_str = final_scores.get(wuxing, '0分')
                    if isinstance(score_str, str):
                        score = float(score_str.replace('分', ''))
                    else:
                        score = float(score_str)

                    # 从状态判定列表中找到对应的状态
                    status = "中和"  # 默认状态
                    if isinstance(status_judgments, list):
                        for status_str in status_judgments:
                            if status_str.startswith(f"{wuxing}:") and ' = ' in status_str:
                                status = status_str.split(' = ')[1].strip()
                                break
                    elif isinstance(status_judgments, dict):
                        status_str = status_judgments.get(wuxing, f"{wuxing}: 0分 = 中和")
                        if ' = ' in status_str:
                            status = status_str.split(' = ')[1].strip()

                    # 如果没有找到状态，根据分数判断 - 使用config配置的阈值
                    if status == "中和":
                        status_thresholds = self.config.get('五行状态判断阈值', {
                            '过强': 4.0,
                            '中和偏强': 2.5,
                            '中和': 1.5,
                            '偏弱': 0.5
                        })

                        if score >= status_thresholds['过强']:
                            status = "过强"
                        elif score >= status_thresholds['中和偏强']:
                            status = "中和偏强"
                        elif score >= status_thresholds['中和']:
                            status = "中和"
                        elif score >= status_thresholds['偏弱']:
                            status = "偏弱"
                        else:
                            status = "很弱"

                    # 根据分数确定季节状态 - 使用config配置的阈值
                    season_thresholds = self.config.get('季节状态判断阈值', {
                        '得令': 3.0,
                        '失令': 1.0
                    })

                    if score >= season_thresholds['得令']:
                        season_status = "得令"
                    elif score <= season_thresholds['失令']:
                        season_status = "失令"
                    else:
                        season_status = "平和"

                    wuxing_scores[wuxing] = {
                        '力量值': score,
                        '状态': status,
                        '季节状态': season_status
                    }

                except Exception as inner_e:
                    print(f"处理{wuxing}时出错: {inner_e}")
                    wuxing_scores[wuxing] = {
                        '力量值': 0,
                        '状态': '很弱',
                        '季节状态': '平和'
                    }

            return wuxing_scores

        except Exception as e:
            print(f"提取五行分数时出错: {e}")
            print(f"可用数据结构: {list(wuxing_details.keys()) if isinstance(wuxing_details, dict) else 'Not a dict'}")
            # 返回默认值
            return {
                '木': {'力量值': 0, '状态': '很弱', '季节状态': '平和'},
                '火': {'力量值': 0, '状态': '很弱', '季节状态': '平和'},
                '土': {'力量值': 0, '状态': '很弱', '季节状态': '平和'},
                '金': {'力量值': 0, '状态': '很弱', '季节状态': '平和'},
                '水': {'力量值': 0, '状态': '很弱', '季节状态': '平和'}
            }

    def _get_liuyue_calculation_details(self, liuyue_result, xiyong_info, target_date, birth_info, dayun, liunian_result):
        """获取流月计算的详细过程"""
        from lunar_python import Solar, Lunar

        details = {
            "第一步_流月干支获取": {},
            "第二步_流月与各方作用分析": {},
            "第三步_流月综合得分计算": {}
        }

        # 第一步：流月干支获取
        target_year = target_date['年']
        target_month = target_date['月']
        solar = Solar.fromYmd(target_year, target_month, 1)
        lunar = solar.getLunar()
        liuyue_ganzhi = lunar.getMonthInGanZhi()
        liuyue_gan, liuyue_zhi = liuyue_ganzhi[0], liuyue_ganzhi[1]

        details["第一步_流月干支获取"] = {
            "计算目标": f"获取{target_year}年{target_month}月的干支",
            "使用工具": "lunar_python库",
            "计算过程": f"Solar.fromYmd({target_year}, {target_month}, 1) -> getLunar() -> getMonthInGanZhi()",
            "计算结果": f"{target_year}年{target_month}月 = {liuyue_ganzhi} (天干:{liuyue_gan}, 地支:{liuyue_zhi})"
        }

        # 第二步：流月与各方作用分析
        sizhu = birth_info['四柱']
        day_gan = sizhu['日柱'][0]
        xiyong_list, ji_list = xiyong_info
        tiangan_info = self.shishen_calculator.tiangan_info

        # 流月天干与各方的作用
        liuyue_tiangan_zuoyong = []

        # 与原局天干作用
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            relation_result = self._calculate_tiangan_relation(liuyue_gan, gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

            if len(relation_result) == 3:  # 天干五合情况
                relation, huashen_or_coefficient, is_hehua_success = relation_result
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                gan_base_score, gan_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
                liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

                final_score, calculation_detail = self._calculate_wuhe_score(
                    liuyue_gan, gan, liuyue_base_score, gan_base_score,
                    huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
                )

                liuyue_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": gan_xiji_type,
                    "计算": calculation_detail["总分"],
                    "得分": final_score
                })
            else:  # 普通生克关系
                relation, coefficient = relation_result[0], relation_result[1]
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
                final_score = base_score * coefficient

                liuyue_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": xiji_type,
                    "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                    "得分": final_score
                })

        # 与大运天干作用
        dayun_gan = dayun[0]
        relation_result = self._calculate_tiangan_relation(liuyue_gan, dayun_gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuyue_gan, dayun_gan, liuyue_base_score, dayun_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuyue_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": dayun_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuyue_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与流年天干作用
        liunian_gan = liunian_result['流年干支'][0]
        relation_result = self._calculate_tiangan_relation(liuyue_gan, liunian_gan, birth_info, dayun, (liunian_gan, liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuyue_gan, liunian_gan, liuyue_base_score, liunian_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuyue_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": liunian_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            base_score, xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuyue_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 流月地支与各方的作用
        liuyue_dizhi_zuoyong = []

        # 与原局地支作用
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]), ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            relation, coefficient = self._calculate_dizhi_relation(liuyue_zhi, zhi, birth_info)
            # 地支通过本气确定十神
            zhi_bengqi = list(self.wuxing_calculator.dizhi_info[zhi]['藏干'].keys())[0]
            shishen = self.shishen_calculator.determine_shishen(day_gan, zhi_bengqi)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuyue_dizhi_zuoyong.append({
                "对象": f"原局{pos}({zhi})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与大运地支作用
        dayun_zhi = dayun[1]
        relation, coefficient = self._calculate_dizhi_relation(liuyue_zhi, dayun_zhi, birth_info)
        dayun_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[dayun_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuyue_dizhi_zuoyong.append({
            "对象": f"大运支({dayun_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        # 与流年地支作用
        liunian_zhi = liunian_result['流年干支'][1]
        relation, coefficient = self._calculate_dizhi_relation(liuyue_zhi, liunian_zhi, birth_info)
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuyue_dizhi_zuoyong.append({
            "对象": f"流年支({liunian_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        details["第二步_流月与各方作用分析"] = {
            "流月干支": f"{liuyue_gan}{liuyue_zhi}",
            "天干作用详情": liuyue_tiangan_zuoyong,
            "地支作用详情": liuyue_dizhi_zuoyong
        }

        # 第三步：流月综合得分计算
        tiangan_total = sum(item["得分"] for item in liuyue_tiangan_zuoyong)
        dizhi_total = sum(item["得分"] for item in liuyue_dizhi_zuoyong)
        liuyue_subtotal = tiangan_total + dizhi_total

        # 流月总得分需要叠加流年和大运的背景影响
        liunian_score = float(liunian_result['总得分'])
        dayun_score = self.config.get('大运背景得分', 0.0)  # 从config获取大运背景得分
        liuyue_total = liuyue_subtotal + liunian_score + dayun_score

        details["第三步_流月综合得分计算"] = {
            "天干小计": f"{tiangan_total:.2f}",
            "地支小计": f"{dizhi_total:.2f}",
            "流月自身得分": f"{liuyue_subtotal:.2f}",
            "流年背景分": f"{liunian_score:.2f}",
            "大运背景分": f"{dayun_score:.2f}",
            "计算公式": "流月自身得分 + 流年背景分 + 大运背景分",
            "总得分": f"{liuyue_total:.2f}"
        }

        # 更新流月结果
        updated_liuyue_result = {
            "流月": f"{target_year}年{target_month}月",
            "流月干支": liuyue_ganzhi,
            "总得分": liuyue_total
        }

        return details, updated_liuyue_result

    def _get_liuri_calculation_details(self, liuri_result, xiyong_info, target_date, birth_info, dayun, liunian_result, liuyue_result):
        """获取流日计算的详细过程"""
        from lunar_python import Solar, Lunar

        details = {
            "第一步_流日干支获取": {},
            "第二步_流日与各方作用分析": {},
            "第三步_流日综合得分计算": {}
        }

        # 第一步：流日干支获取
        target_year = target_date['年']
        target_month = target_date['月']
        target_day = target_date['日']
        solar = Solar.fromYmd(target_year, target_month, target_day)
        lunar = solar.getLunar()
        liuri_ganzhi = lunar.getDayInGanZhi()
        liuri_gan, liuri_zhi = liuri_ganzhi[0], liuri_ganzhi[1]

        details["第一步_流日干支获取"] = {
            "计算目标": f"获取{target_year}年{target_month}月{target_day}日的干支",
            "使用工具": "lunar_python库",
            "计算过程": f"Solar.fromYmd({target_year}, {target_month}, {target_day}) -> getLunar() -> getDayInGanZhi()",
            "计算结果": f"{target_year}年{target_month}月{target_day}日 = {liuri_ganzhi} (天干:{liuri_gan}, 地支:{liuri_zhi})"
        }

        # 第二步：流日与各方作用分析（完整版）
        sizhu = birth_info['四柱']
        day_gan = sizhu['日柱'][0]
        xiyong_list, ji_list = xiyong_info

        # 流日天干与各方天干的作用
        liuri_tiangan_zuoyong = []

        # 与原局天干作用
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]), ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            relation_result = self._calculate_tiangan_relation(liuri_gan, gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

            if len(relation_result) == 3:  # 天干五合情况
                relation, huashen_or_coefficient, is_hehua_success = relation_result
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                gan_base_score, gan_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

                liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
                liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

                final_score, calculation_detail = self._calculate_wuhe_score(
                    liuri_gan, gan, liuri_base_score, gan_base_score,
                    huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
                )

                liuri_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": gan_xiji_type,
                    "计算": calculation_detail["总分"],
                    "得分": final_score
                })
            else:  # 普通生克关系
                relation, coefficient = relation_result[0], relation_result[1]
                shishen = self.shishen_calculator.determine_shishen(day_gan, gan)
                base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
                final_score = base_score * coefficient

                liuri_tiangan_zuoyong.append({
                    "对象": f"原局{pos}({gan})",
                    "关系": relation,
                    "十神": shishen,
                    "喜忌": xiji_type,
                    "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                    "得分": final_score
                })

        # 与大运天干作用
        dayun_gan = dayun[0]
        relation_result = self._calculate_tiangan_relation(liuri_gan, dayun_gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            dayun_base_score, dayun_xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)

            liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
            liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuri_gan, dayun_gan, liuri_base_score, dayun_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuri_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": dayun_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_gan)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_tiangan_zuoyong.append({
                "对象": f"大运干({dayun_gan})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与流年天干作用
        liunian_gan = liunian_result['流年干支'][0]
        relation_result = self._calculate_tiangan_relation(liuri_gan, liunian_gan, birth_info, dayun, (liunian_gan, liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            liunian_base_score, liunian_xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)

            liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
            liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuri_gan, liunian_gan, liuri_base_score, liunian_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuri_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": liunian_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            liunian_shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_gan)
            base_score, xiji_type = self._get_xiji_score(liunian_shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_tiangan_zuoyong.append({
                "对象": f"流年干({liunian_gan})",
                "关系": relation,
                "十神": liunian_shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与流月天干作用
        liuyue_gan = liuyue_result['流月干支'][0]
        relation_result = self._calculate_tiangan_relation(liuri_gan, liuyue_gan, birth_info, dayun, (liunian_result['流年干支'][0], liunian_result['流年干支'][1]))

        if len(relation_result) == 3:  # 天干五合情况
            relation, huashen_or_coefficient, is_hehua_success = relation_result
            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            liuyue_base_score, liuyue_xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)

            liuri_shishen = self.shishen_calculator.determine_shishen(day_gan, liuri_gan)
            liuri_base_score, liuri_xiji_type = self._get_xiji_score(liuri_shishen, xiyong_list, ji_list)

            final_score, calculation_detail = self._calculate_wuhe_score(
                liuri_gan, liuyue_gan, liuri_base_score, liuyue_base_score,
                huashen_or_coefficient, is_hehua_success, xiyong_list, ji_list, day_gan
            )

            liuri_tiangan_zuoyong.append({
                "对象": f"流月干({liuyue_gan})",
                "关系": relation,
                "十神": liuyue_shishen,
                "喜忌": liuyue_xiji_type,
                "计算": calculation_detail["总分"],
                "得分": final_score
            })
        else:  # 普通生克关系
            relation, coefficient = relation_result[0], relation_result[1]
            liuyue_shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_gan)
            base_score, xiji_type = self._get_xiji_score(liuyue_shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_tiangan_zuoyong.append({
                "对象": f"流月干({liuyue_gan})",
                "关系": relation,
                "十神": liuyue_shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 流日地支与各方地支的作用
        liuri_dizhi_zuoyong = []

        # 与原局地支作用
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]), ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, zhi, birth_info)
            # 地支通过本气确定十神
            zhi_bengqi = list(self.wuxing_calculator.dizhi_info[zhi]['藏干'].keys())[0]
            shishen = self.shishen_calculator.determine_shishen(day_gan, zhi_bengqi)
            base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
            final_score = base_score * coefficient

            liuri_dizhi_zuoyong.append({
                "对象": f"原局{pos}({zhi})",
                "关系": relation,
                "十神": shishen,
                "喜忌": xiji_type,
                "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
                "得分": final_score
            })

        # 与大运地支作用
        dayun_zhi = dayun[1]
        relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, dayun_zhi, birth_info)
        dayun_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[dayun_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, dayun_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuri_dizhi_zuoyong.append({
            "对象": f"大运支({dayun_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        # 与流年地支作用
        liunian_zhi = liunian_result['流年干支'][1]
        relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, liunian_zhi, birth_info)
        liunian_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, liunian_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuri_dizhi_zuoyong.append({
            "对象": f"流年支({liunian_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        # 与流月地支作用
        liuyue_zhi = liuyue_result['流月干支'][1]
        relation, coefficient = self._calculate_dizhi_relation(liuri_zhi, liuyue_zhi, birth_info)
        liuyue_zhi_bengqi = list(self.wuxing_calculator.dizhi_info[liuyue_zhi]['藏干'].keys())[0]
        shishen = self.shishen_calculator.determine_shishen(day_gan, liuyue_zhi_bengqi)
        base_score, xiji_type = self._get_xiji_score(shishen, xiyong_list, ji_list)
        final_score = base_score * coefficient

        liuri_dizhi_zuoyong.append({
            "对象": f"流月支({liuyue_zhi})",
            "关系": relation,
            "十神": shishen,
            "喜忌": xiji_type,
            "计算": f"{base_score} × {coefficient} = {final_score:.2f}",
            "得分": final_score
        })

        details["第二步_流日与各方作用分析"] = {
            "流日干支": f"{liuri_gan}{liuri_zhi}",
            "天干作用详情": liuri_tiangan_zuoyong,
            "地支作用详情": liuri_dizhi_zuoyong
        }

        # 第三步：流日综合得分计算
        tiangan_total = sum(item["得分"] for item in liuri_tiangan_zuoyong)
        dizhi_total = sum(item["得分"] for item in liuri_dizhi_zuoyong)
        liuri_subtotal = tiangan_total + dizhi_total

        # 流日总得分需要叠加流年、流月和大运的背景影响
        liunian_score = float(liunian_result['总得分'])
        liuyue_score = float(liuyue_result['总得分'])
        dayun_score = self.config.get('大运背景得分', 0.0)  # 从config获取大运背景得分
        liuri_total = liuri_subtotal + liunian_score + liuyue_score + dayun_score

        details["第三步_流日综合得分计算"] = {
            "天干小计": f"{tiangan_total:.2f}",
            "地支小计": f"{dizhi_total:.2f}",
            "流日自身得分": f"{liuri_subtotal:.2f}",
            "流年背景分": f"{liunian_score:.2f}",
            "流月背景分": f"{liuyue_score:.2f}",
            "大运背景分": f"{dayun_score:.2f}",
            "计算公式": "流日自身得分 + 流年背景分 + 流月背景分 + 大运背景分",
            "总得分": f"{liuri_total:.2f}"
        }

        # 更新流日结果
        updated_liuri_result = {
            "流日": f"{target_year}年{target_month}月{target_day}日",
            "流日干支": liuri_ganzhi,
            "总得分": liuri_total
        }

        return details, updated_liuri_result

    def _is_same_help(self, gan1, gan2, day_gan):
        """判断是否为天干比劫帮扶关系

        注意：这应该是一个特殊的强化关系，不是普通的比肩劫财
        只有在特定条件下才触发，比如流年干与日主同五行且有特殊加强作用
        """
        tiangan_info = self.shishen_calculator.tiangan_info
        day_wuxing = tiangan_info[day_gan]['五行']

        # 检查是否有天干与日主同五行（比肩或劫财）
        gan1_wuxing = tiangan_info[gan1]['五行']
        gan2_wuxing = tiangan_info[gan2]['五行']

        # 只有当其中一个是日主，另一个与日主同五行时才触发特殊比劫帮扶
        if gan1 == day_gan and gan2_wuxing == day_wuxing:
            return True
        elif gan2 == day_gan and gan1_wuxing == day_wuxing:
            return True
        return False

    def _is_generate_help(self, gan1, gan2, day_gan):
        """判断是否为印星生扶关系

        注意：这应该是一个特殊的强化关系，不是普通的印星生助
        只有在特定条件下才触发，比如流年干生助日主且有特殊加强作用
        """
        tiangan_info = self.shishen_calculator.tiangan_info
        day_wuxing = tiangan_info[day_gan]['五行']

        # 检查是否有天干生助日主（印星关系）
        gan1_wuxing = tiangan_info[gan1]['五行']
        gan2_wuxing = tiangan_info[gan2]['五行']

        # 只有当其中一个是日主，另一个生助日主时才触发特殊印星生扶
        if gan1 == day_gan and self.wuxing_calculator.wuxing_sheng.get(gan2_wuxing) == day_wuxing:
            return True
        elif gan2 == day_gan and self.wuxing_calculator.wuxing_sheng.get(gan1_wuxing) == day_wuxing:
            return True
        return False

    def _calculate_tiangan_relation(self, gan1, gan2, birth_info=None, dayun=None, liunian_ganzhi=None):
        """计算天干关系，包含详细的天干五合处理"""
        tiangan_info = self.shishen_calculator.tiangan_info

        # 检查天干五合关系
        wuhe_pairs = {
            ('甲', '己'): '土',
            ('乙', '庚'): '金',
            ('丙', '辛'): '水',
            ('丁', '壬'): '木',
            ('戊', '癸'): '火'
        }

        # 查找五合关系
        wuhe_result = None
        for pair, huashen in wuhe_pairs.items():
            if (gan1, gan2) in [pair, pair[::-1]]:
                wuhe_result = huashen
                break

        if wuhe_result:
            # 判断化神是否旺
            is_huashen_wang = self._check_huashen_wang(wuhe_result, birth_info, dayun, liunian_ganzhi)

            if is_huashen_wang:
                return f"天干五合化{wuhe_result}(成功)", wuhe_result, True
            else:
                return f"天干五合合绊", None, False

        # 检查特殊扶持关系（对身弱格局重要）
        if birth_info:
            day_gan = birth_info['四柱']['日柱'][0]

            # 1. 天干比劫帮扶 (SAME_HELP)
            if self._is_same_help(gan1, gan2, day_gan):
                return "天干比劫帮扶", self.config['地支关系影响系数']['天干比劫帮扶']

            # 2. 印星生扶 (GENERATE_HELP)
            if self._is_generate_help(gan1, gan2, day_gan):
                return "印星生扶", self.config['地支关系影响系数']['印星生扶']

        # 普通五行生克关系 - 从配置文件读取权重
        wuxing1 = tiangan_info[gan1]['五行']
        wuxing2 = tiangan_info[gan2]['五行']

        if wuxing1 == wuxing2:
            return "比和", self.config['地支关系影响系数'].get('比和', 1.0)
        elif self.wuxing_calculator.wuxing_sheng.get(wuxing1) == wuxing2:
            return "相生", self.config['地支关系影响系数'].get('相生', 1.0)
        elif self.wuxing_calculator.wuxing_ke.get(wuxing1) == wuxing2:
            return "相克", self.config['地支关系影响系数'].get('相克', 1.0)
        else:
            return "无直接关系", self.config['地支关系影响系数'].get('无直接关系', 1.0)

    def _check_huashen_wang(self, huashen_wuxing, birth_info, dayun, liunian_ganzhi):
        """判断化神是否旺（有强根）"""
        # 化神对应的地支
        wuxing_to_dizhi = {
            '木': ['寅', '卯'],
            '火': ['巳', '午'],
            '土': ['辰', '戌', '丑', '未'],
            '金': ['申', '酉'],
            '水': ['子', '亥']
        }

        huashen_dizhi = wuxing_to_dizhi.get(huashen_wuxing, [])
        huashen_count = 0

        # 统计原局地支中化神的数量
        if birth_info:
            sizhu = birth_info['四柱']
            for pos in ['年柱', '月柱', '日柱', '时柱']:
                zhi = sizhu[pos][1]
                if zhi in huashen_dizhi:
                    huashen_count += 1

        # 统计大运地支
        if dayun and len(dayun) > 1:
            dayun_zhi = dayun[1]
            if dayun_zhi in huashen_dizhi:
                huashen_count += 1

        # 统计流年地支
        if liunian_ganzhi and len(liunian_ganzhi) > 1:
            liunian_zhi = liunian_ganzhi[1]
            if liunian_zhi in huashen_dizhi:
                huashen_count += 1

        # 判断标准：化神地支数量 >= 2 认为化神旺
        return huashen_count >= 2

    def _calculate_dizhi_relation(self, zhi1, zhi2, birth_info=None):
        """计算地支关系，包含地支根气支持"""
        # 检查地支根气关系（只对身弱格局重要，身强格局不需要）
        if birth_info and self._is_root_support(zhi1, zhi2, birth_info):
            # 检查身强身弱，身强格局不需要地支根气加分
            shishen_info = birth_info.get('十神分析', {})
            body_analysis = shishen_info.get('身强身弱', {})
            if body_analysis.get('结果') == '身弱':
                return "地支根气", self.config['地支关系影响系数']['地支根气']

        # 地支六冲
        liuchong_pairs = [('子', '午'), ('丑', '未'), ('寅', '申'), ('卯', '酉'), ('辰', '戌'), ('巳', '亥')]
        if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in liuchong_pairs):
            return "地支六冲", self.config['地支关系影响系数']['地支六冲']

        # 地支六合
        liuhe_pairs = [('子', '丑'), ('寅', '亥'), ('卯', '戌'), ('辰', '酉'), ('巳', '申'), ('午', '未')]
        if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in liuhe_pairs):
            return "地支六合", self.config['地支关系影响系数']['地支六合']

        # 地支相害
        xianghai_pairs = [('子', '未'), ('丑', '午'), ('寅', '巳'), ('卯', '辰'), ('申', '亥'), ('酉', '戌')]
        if any((zhi1, zhi2) in [pair, pair[::-1]] for pair in xianghai_pairs):
            return "地支相害", self.config['地支关系影响系数']['地支相害']

        # 地支相刑
        if {zhi1, zhi2}.issubset({'寅', '巳', '申'}):
            return "地支相刑", self.config['地支关系影响系数']['地支相刑']
        elif {zhi1, zhi2}.issubset({'丑', '未', '戌'}):
            return "地支相刑", self.config['地支关系影响系数']['地支相刑']
        elif {zhi1, zhi2} == {'子', '卯'}:
            return "地支相刑", self.config['地支关系影响系数']['地支相刑']

        # 普通生克关系 - 从配置文件读取权重
        return "普通生克", self.config['地支关系影响系数'].get('普通生克', 1.0)

    def _is_root_support(self, zhi1, zhi2, birth_info):
        """判断是否为地支根气关系

        Args:
            zhi1: 地支1（通常是流年、流月、流日支）
            zhi2: 地支2（通常是原局、大运支）
            birth_info: 出生信息

        Returns:
            bool: 是否有地支为日主的根气地支
        """
        day_gan = birth_info['四柱']['日柱'][0]

        # 日主的禄、刃、长生、墓库之地
        root_support_map = {
            '甲': ['寅', '卯', '亥', '未'],  # 禄寅、刃卯、长生亥、墓未
            '乙': ['卯', '寅', '午', '戌'],  # 禄卯、刃寅、长生午、墓戌
            '丙': ['巳', '午', '寅', '戌'],  # 禄巳、刃午、长生寅、墓戌
            '丁': ['午', '巳', '酉', '丑'],  # 禄午、刃巳、长生酉、墓丑
            '戊': ['巳', '午', '寅', '戌'],  # 禄巳、刃午、长生寅、墓戌
            '己': ['午', '巳', '酉', '丑'],  # 禄午、刃巳、长生酉、墓丑
            '庚': ['申', '酉', '巳', '丑'],  # 禄申、刃酉、长生巳、墓丑
            '辛': ['酉', '申', '子', '辰'],  # 禄酉、刃申、长生子、墓辰
            '壬': ['亥', '子', '申', '辰'],  # 禄亥、刃子、长生申、墓辰
            '癸': ['子', '亥', '卯', '未']   # 禄子、刃亥、长生卯、墓未
        }

        root_zhis = root_support_map.get(day_gan, [])
        # 只有当流年地支是日主的根气地支时才算地支根气
        # 这样避免了原局地支之间的错误判断
        return zhi1 in root_zhis

    def _get_xiji_score(self, shishen, xiyong_list, ji_list):
        """获取喜忌神得分"""
        is_xiyong = any(shishen == item[0] for item in xiyong_list)
        is_ji = any(shishen == item[0] for item in ji_list)

        if is_xiyong:
            return 1.0, "喜用神"
        elif is_ji:
            return -1.0, "忌神"
        else:
            return 0.0, "中性"

    def _calculate_wuhe_score(self, gan1, gan2, gan1_base_score, gan2_base_score, huashen_wuxing, is_hehua_success, xiyong_list, ji_list, day_gan):
        """计算天干五合的详细得分"""
        wuhe_coefficient = self.config['地支关系影响系数']['天干五合']  # 1.5

        if is_hehua_success:
            # 合化成功的计算：(合化后化神分 - 合化前两干分) × 1.5
            # 1. 计算化神分数
            huashen_shishen = self._get_wuxing_shishen(huashen_wuxing, day_gan)
            huashen_base_score, huashen_xiji = self._get_xiji_score(huashen_shishen, xiyong_list, ji_list)

            # 2. 计算合化前后的分数变化
            hehua_hou_score = huashen_base_score  # 合化后的分数
            hehua_qian_score = gan1_base_score + gan2_base_score  # 合化前的分数
            score_change = hehua_hou_score - hehua_qian_score

            # 3. 乘以权重系数
            total_score = score_change * wuhe_coefficient

            calculation_detail = {
                "类型": "合化成功",
                "化神": f"{huashen_wuxing}({huashen_shishen})",
                "合化后": f"{huashen_base_score}",
                "合化前": f"{gan1_base_score} + {gan2_base_score} = {hehua_qian_score:.2f}",
                "分数变化": f"{hehua_hou_score:.2f} - ({hehua_qian_score:.2f}) = {score_change:.2f}",
                "总分": f"{score_change:.2f} × {wuhe_coefficient} = {total_score:.2f}"
            }

        else:
            # 合绊的计算：(甲基准分 + 己基准分) × 合绊系数 × 天干五合系数
            heban_coefficient = self.config.get('天干五合合绊系数', -0.33)
            normal_total = gan1_base_score + gan2_base_score
            total_score = normal_total * heban_coefficient * wuhe_coefficient

            calculation_detail = {
                "类型": "合绊",
                "正常总分": f"{gan1_base_score} + {gan2_base_score} = {normal_total:.2f}",
                "合绊公式": f"({normal_total:.2f}) × {heban_coefficient} × {wuhe_coefficient} = {total_score:.2f}",
                "总分": f"{total_score:.2f}"
            }

        return total_score, calculation_detail

    def _get_wuxing_shishen(self, wuxing, day_gan):
        """根据五行和日干确定十神"""
        day_wuxing = self.shishen_calculator.tiangan_info[day_gan]['五行']

        if wuxing == day_wuxing:
            return "比肩"  # 简化处理，实际应该区分比肩和劫财
        elif self.wuxing_calculator.wuxing_sheng.get(day_wuxing) == wuxing:
            return "食神"  # 简化处理，实际应该区分食神和伤官
        elif self.wuxing_calculator.wuxing_sheng.get(wuxing) == day_wuxing:
            return "正印"  # 简化处理，实际应该区分正印和偏印
        elif self.wuxing_calculator.wuxing_ke.get(day_wuxing) == wuxing:
            return "正财"  # 简化处理，实际应该区分正财和偏财
        elif self.wuxing_calculator.wuxing_ke.get(wuxing) == day_wuxing:
            return "正官"  # 简化处理，实际应该区分正官和七杀
        else:
            return "未知"

    def analyze_marriage_compatibility(self):
        """合婚分析（预留接口）"""
        return {
            "状态": "功能暂未实现",
            "说明": "合婚计算模块预留接口，后续开发"
        }
    
    def format_comprehensive_output(self, result, show_details=True):
        """格式化综合输出结果"""
        if "错误" in result:
            return f"分析失败：{result['错误']}"
        
        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        dayun_info = result["大运流年"]
        target_date = self.config['流年流月流日分析日期']
        
        output = f"""
{'='*50}
八字占卜系统 - 综合分析报告
{'='*50}

【基本信息】
姓名：{basic_info['姓名']}
性别：男  # 可根据需要调整
出生地：{basic_info['出生地']} (经度: {basic_info['经度']}°)

出生时间信息：
输入时间：{basic_info['输入时间']}
北京时间：{basic_info['北京时间']}
阴历时间：{basic_info['阴历信息']['阴历年']}年{basic_info['阴历信息']['阴历月']}月{basic_info['阴历信息']['阴历日']}日 {basic_info['阴历信息']['阴历时']}时
出生节气：{basic_info['阴历信息']['节气']}

夏令时处理：
{basic_info['夏令时信息']['说明']}"""

        # 如果是夏令时期间，显示详细信息
        if basic_info['夏令时信息']['是否夏令时期间']:
            output += f"""
夏令时开始：{basic_info['夏令时信息']['夏令时开始']}
夏令时结束：{basic_info['夏令时信息']['夏令时结束']}
时间转换：{basic_info['夏令时信息']['输入时间']} → {basic_info['夏令时信息']['北京时间']}"""

        output += f"""

真太阳时计算详细过程：
1. 北京时间：{basic_info['北京时间']}
2. 经度时差：{basic_info['经度时差']} (公式: (当地经度 - 120°) × 4分钟)
3. 均时差：{basic_info['均时差']} (地球椭圆轨道修正)
4. 真太阳时：{basic_info['真太阳时']} (北京时间 + 经度时差 + 均时差)

【四柱八字】
年柱：{basic_info['四柱']['年柱']}
月柱：{basic_info['四柱']['月柱']}
日柱：{basic_info['四柱']['日柱']}
时柱：{basic_info['四柱']['时柱']}

【五行分析】
当前季节：{wuxing_info['当前季节']}

五行计算详细过程：
1. 天干五行统计：
"""
        for wuxing, positions in wuxing_info['计算详情']['天干五行统计'].items():
            output += f"   {wuxing}：{', '.join(positions)}\n"

        output += f"""
2. 地支本气统计：
"""
        for wuxing, positions in wuxing_info['计算详情']['地支本气统计'].items():
            output += f"   {wuxing}：{', '.join(positions)}\n"

        output += f"""
3. 地支藏干详情：
"""
        for position, canggan_list in wuxing_info['计算详情']['地支藏干详情'].items():
            output += f"   {position}：{', '.join(canggan_list)}\n"

        output += f"""
4. 季节旺度影响：
   当前季节：{wuxing_info['计算详情']['季节旺度影响']['当前季节']}
   得令（旺）：{wuxing_info['计算详情']['季节旺度影响']['旺']}
   失令（死）：{wuxing_info['计算详情']['季节旺度影响']['死']}
   平和：{wuxing_info['计算详情']['季节旺度影响']['其他']}

5. 五行力量详细计算过程：

第一步 - 基础分数统计：
   天干统计：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第一步_基础分数统计']['天干统计']:
            output += f"     {detail}\n"

        output += f"""   地支本气统计：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第一步_基础分数统计']['地支本气统计']:
            output += f"     {detail}\n"

        output += f"""   基础分数：{wuxing_info['计算详情']['五行力量详细计算']['第一步_基础分数统计']['基础分数']}

第二步 - 季节旺度调整：
   调整公式：{wuxing_info['计算详情']['五行力量详细计算']['第二步_季节旺度调整']['调整公式']}
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第二步_季节旺度调整']['详细计算']:
            output += f"     {detail}\n"

        output += f"""   调整后分数：{wuxing_info['计算详情']['五行力量详细计算']['第二步_季节旺度调整']['调整后分数']}

第三步 - 藏干权重计算：
   计算说明：{wuxing_info['计算详情']['五行力量详细计算']['第三步_藏干权重计算']['计算说明']}
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第三步_藏干权重计算']['详细计算']:
            output += f"     {detail}\n"

        output += f"""   藏干得分：{wuxing_info['计算详情']['五行力量详细计算']['第三步_藏干权重计算']['藏干得分']}

第四步 - 生扶克制计算：
   基础分数：{wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['基础分数']}
   生扶系数：{wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['生扶系数']}
   克制系数：{wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['克制系数']}
   生扶计算：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['生扶计算']:
            output += f"     {detail}\n"

        output += f"""   克制计算：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第四步_生扶克制计算']['克制计算']:
            output += f"     {detail}\n"

        output += f"""
第五步 - 最终力量汇总：
   最终得分：{wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['最终得分']}
   总力量：{wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['总力量']}
   力量占比：{wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['力量占比']}
   状态判定：
"""
        for detail in wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['状态判定']:
            output += f"     {detail}\n"




        
        # 获取各部分信息
        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        paipan_info = result["排盘信息"]
        dayun_info = result["大运流年"]

        # 获取身强身弱分析结果
        body_analysis = shishen_info['身强身弱']
        xiyong_list, ji_list = shishen_info['喜用神忌神']

        output += f"""
【十神分析】
日主信息：{shishen_info['计算详情']['日主信息']['日干']} ({shishen_info['计算详情']['日主信息']['五行']}{shishen_info['计算详情']['日主信息']['阴阳']})

十神计算详细过程：
1. 天干十神分析：
"""
        for position, analysis in shishen_info['计算详情']['天干十神分析'].items():
            output += f"   {position}：{analysis}\n"

        output += f"""
2. 地支藏干十神分析：
"""
        for position, canggan_list in shishen_info['计算详情']['地支藏干十神分析'].items():
            output += f"   {position}：\n"
            for canggan_analysis in canggan_list:
                output += f"     {canggan_analysis}\n"

        output += f"""
3. 十神权重汇总：
"""
        for shishen, weight in shishen_info['十神权重'].items():
            output += f"   {shishen}：{weight:.2f}\n"

        # 显示传统综合评分法的结果
        if body_analysis.get('method') == 'traditional':
            traditional_details = body_analysis['details']
            output += f"""
4. 身强身弱判断（传统综合评分法）：
   综合得分：{body_analysis['total_score']:.2f}分 (50分为界限)
   判断结果：{body_analysis['strength']}

   详细分析：
   - 得令（月令，权重50%）：{traditional_details['得令']['得分']:.0f}分 → 权重后{traditional_details['得令']['权重后得分']:.1f}分 ({traditional_details['得令']['得令状态']})
   - 得地（地支根气，权重30%）：{traditional_details['得地']['得分']:.0f}分 → 权重后{traditional_details['得地']['权重后得分']:.1f}分"""

            # 添加得地详情
            if '详情' in traditional_details['得地']:
                for detail in traditional_details['得地']['详情']:
                    output += f"\n     {detail}"

            output += f"""
   - 得势（天干比劫，权重15%）：{traditional_details['得势']['得分']:.0f}分 → 权重后{traditional_details['得势']['权重后得分']:.1f}分"""

            # 添加得势详情
            if '详情' in traditional_details['得势']:
                for detail in traditional_details['得势']['详情']:
                    output += f"\n     {detail}"

            output += f"""
   - 得生（天干印星，权重5%）：{traditional_details['得生']['得分']:.0f}分 → 权重后{traditional_details['得生']['权重后得分']:.1f}分"""

            # 添加得生详情
            if '详情' in traditional_details['得生']:
                for detail in traditional_details['得生']['详情']:
                    output += f"\n     {detail}"

            output += "\n"
        else:
            # 兼容旧格式（如果还有的话）
            body_strength, support_power, drain_power = body_analysis
            output += f"""
4. 身强身弱判断：
   生扶日主力量：{support_power:.2f} (正印+偏印+比肩+劫财)
   克泄耗日主力量：{drain_power:.2f} (正官+七杀+食神+伤官+正财+偏财)
   判断结果：{body_strength}
"""
        
        output += f"""
喜用神："""
        for shishen, weight in xiyong_list[:4]:
            output += f" {shishen}({weight:.2f})"

        output += f"""
忌神："""
        for shishen, weight in ji_list[:4]:
            output += f" {shishen}({weight:.2f})"

        # 添加排盘信息
        output += f"""

【排盘信息】
"""
        # 按照图片中的顺序显示：年柱、月柱、日柱、时柱
        pillar_names = ['年柱', '月柱', '日柱', '时柱']
        pillar_display = ['年柱', '月柱', '日柱', '时柱']

        # 表格标题行
        output += "        "
        for display_name in pillar_display:
            output += f"{display_name:>8}"
        output += "\n"

        # 主星行：日柱显示主星，其他柱显示天干对应的十神
        output += "主星    "
        for pillar_name in pillar_names:
            main_star = paipan_info[pillar_name]['主星']
            if main_star:
                output += f"{main_star:>8}"
            else:
                output += f"{'':>8}"
        output += "\n"

        # 天干行
        output += "天干    "
        for pillar_name in pillar_names:
            gan = paipan_info[pillar_name]['干支'][0]
            output += f"{gan:>8}"
        output += "\n"

        # 地支行
        output += "地支    "
        for pillar_name in pillar_names:
            zhi = paipan_info[pillar_name]['干支'][1]
            output += f"{zhi:>8}"
        output += "\n"

        # 副星行
        output += "副星    "
        for pillar_name in pillar_names:
            fuxing = paipan_info[pillar_name].get('副星', '未知')
            output += f"{fuxing:>8}"
        output += "\n"

        # 星运行
        output += "星运    "
        for pillar_name in pillar_names:
            xingyun = paipan_info[pillar_name]['星运']
            output += f"{xingyun:>8}"
        output += "\n"

        # 自坐行（仅日柱显示）
        output += "自坐    "
        for pillar_name in pillar_names:
            if pillar_name == '日柱' and paipan_info[pillar_name]['自坐']:
                output += f"{paipan_info[pillar_name]['自坐']:>8}"
            else:
                output += f"{'':>8}"
        output += "\n"

        # 空亡行：每个柱位分别显示空亡
        output += "空亡    "
        for pillar_name in pillar_names:
            kongwang_list = paipan_info[pillar_name]['空亡']
            if kongwang_list:
                kongwang_str = '、'.join(kongwang_list)
                # 如果空亡字符串太长，只显示第一个
                if len(kongwang_str) > 6:
                    kongwang_str = kongwang_list[0] + '等'
                output += f"{kongwang_str:>8}"
            else:
                output += f"{'无':>8}"
        output += "\n"

        # 纳音行
        output += "纳音    "
        for pillar_name in pillar_names:
            nayin = paipan_info[pillar_name]['纳音']
            # 纳音名称较长，取前4个字符
            nayin_short = nayin[:4] if len(nayin) > 4 else nayin
            output += f"{nayin_short:>8}"
        output += "\n"

        # 神煞行
        output += "神煞    "
        for pillar_name in pillar_names:
            shensha_list = paipan_info[pillar_name]['神煞']
            if shensha_list:
                # 如果神煞太多，只显示前2个
                shensha_str = '、'.join(shensha_list[:2])
                if len(shensha_list) > 2:
                    shensha_str += '等'
            else:
                shensha_str = '无'
            output += f"{shensha_str:>8}"
        output += "\n"
        
        output += f"""

夫妻宫：{shishen_info['夫妻宫配偶星']['夫妻宫']}
配偶星：{shishen_info['夫妻宫配偶星']['配偶星']}

【大运流年分析】

大运起运计算详细过程：
1. 起运规则判断：
   年干：{dayun_info['计算详情']['起运规则判断']['年干']}
   性别：{dayun_info['计算详情']['起运规则判断']['性别']}
   适用规则：{dayun_info['计算详情']['起运规则判断']['适用规则']}
   排列方向：{dayun_info['计算详情']['起运规则判断']['排列方向']}

2. 节气距离计算：
   {dayun_info['计算详情']['节气距离计算']['距离节气天数']}
   计算方法：{dayun_info['计算详情']['节气距离计算']['计算方法']}

3. 起运时间换算：
   换算公式：{dayun_info['计算详情']['起运时间换算']['换算公式']}
   计算过程：{dayun_info['计算详情']['起运时间换算']['计算过程']}
   月份计算：{dayun_info['计算详情']['起运时间换算']['月份计算']}
   最终结果：{dayun_info['计算详情']['起运时间换算']['最终结果']}

4. 大运排列规则：
   起始月柱：{dayun_info['计算详情']['大运排列规则']['起始月柱']}
   排列方向：{dayun_info['计算详情']['大运排列规则']['排列方向']}
   每步大运：{dayun_info['计算详情']['大运排列规则']['每步大运']}

5. 大运序列：
"""

        for dayun_item in dayun_info['大运序列']:
            if isinstance(dayun_item, dict):
                # 新的字典格式
                output += f"   {dayun_item['年龄范围']}：{dayun_item['大运']}运\n"
            else:
                # 旧的元组格式（兼容性）
                dayun, (start_age, end_age) = dayun_item
                output += f"   {start_age}-{end_age}岁：{dayun}运\n"
        
        liunian = dayun_info['流年分析']
        liuyue = dayun_info['流月分析']
        liuri = dayun_info['流日分析']

        output += f"""
6. 流年流月流日计算详细过程：

流年计算详情：
   流年：{dayun_info['流年分析']['流年']} ({dayun_info['流年分析']['流年干支']})
   总得分：{dayun_info['流年分析']['总得分']}
   吉凶等级：{dayun_info['流年分析']['吉凶等级']}
   计算说明：{dayun_info['流年计算详情']['计算说明']}"""

        output += f"""
流月计算详情：
   流月：{dayun_info['流月分析']['流月']} ({dayun_info['流月分析']['流月干支']})
   总得分：{dayun_info['流月分析']['总得分']}
   计算说明：{dayun_info['流月计算详情']['计算说明']}

流日计算详情：
   流日：{dayun_info['流日分析']['流日']} ({dayun_info['流日分析']['流日干支']})
   总得分：{dayun_info['流日分析']['总得分']}
   计算说明：{dayun_info['流日计算详情']['计算说明']}"""

        output += f"""   地支作用详情：
"""
        for zuoyong in dayun_info['流日计算详情']['第二步_流日与各方作用分析']['地支作用详情']:
            output += f"     {zuoyong['对象']} - {zuoyong['关系']}\n"
            output += f"       十神：{zuoyong['十神']} ({zuoyong['喜忌']})\n"
            output += f"       计算：{zuoyong['计算']}\n"

        output += f"""
流日综合得分计算：
   天干小计：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['天干小计']}
   地支小计：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['地支小计']}
   流日自身得分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['流日自身得分']}
   流年背景分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['流年背景分']}
   流月背景分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['流月背景分']}
   大运背景分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['大运背景分']}
   计算公式：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['计算公式']}
   总得分：{dayun_info['流日计算详情']['第三步_流日综合得分计算']['总得分']}

9. 流年流月流日分析结果：

流年分析：{dayun_info['流年分析']['流年']} ({dayun_info['流年分析']['流年干支']})
  总得分：{dayun_info['流年分析']['总得分']:.2f}
  吉凶等级：{dayun_info['流年分析']['吉凶等级']}
  运势解读：{dayun_info['流年分析']['运势解读']}

流月分析：{dayun_info['流月分析']['流月']} ({dayun_info['流月分析']['流月干支']})
  总得分：{dayun_info['流月分析']['总得分']:.2f}

流日分析：{dayun_info['流日分析']['流日']} ({dayun_info['流日分析']['流日干支']})
  总得分：{dayun_info['流日分析']['总得分']:.2f}

【合婚分析】
{result['合婚分析']['状态']}
{result['合婚分析']['说明']}

{'='*50}
分析完成
{'='*50}
"""
        
        return output

    def format_simple_output(self, result):
        """格式化简洁版输出结果（不包含详细计算过程）"""
        if "错误" in result:
            return f"分析失败：{result['错误']}"

        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        dayun_info = result["大运流年"]

        # 获取各部分信息
        basic_info = result["基本信息"]
        wuxing_info = result["五行分析"]
        shishen_info = result["十神分析"]
        paipan_info = result["排盘信息"]
        dayun_info = result["大运流年"]

        # 获取身强身弱分析结果
        body_analysis = shishen_info['身强身弱']
        xiyong_list, ji_list = shishen_info['喜用神忌神']

        output = f"""
{'='*50}
八字占卜系统 - 分析报告
{'='*50}

【基本信息】
姓名：{basic_info['姓名']}
出生地：{basic_info['出生地']} (经度: {basic_info['经度']}°)
输入时间：{basic_info['输入时间']}
北京时间：{basic_info['北京时间']}
阴历时间：{basic_info['阴历信息']['阴历年']}年{basic_info['阴历信息']['阴历月']}月{basic_info['阴历信息']['阴历日']}日 {basic_info['阴历信息']['阴历时']}时
出生节气：{basic_info['阴历信息']['节气']}
夏令时：{basic_info['夏令时信息']['说明']}
真太阳时：{basic_info['真太阳时']}

【四柱八字】
年柱：{basic_info['四柱']['年柱']}
月柱：{basic_info['四柱']['月柱']}
日柱：{basic_info['四柱']['日柱']}
时柱：{basic_info['四柱']['时柱']}

【五行分析】
当前季节：{wuxing_info['当前季节']}
五行力量分布：
"""

        # 从详细计算中获取五行力量分布
        try:
            # 优先使用五行力量数据
            if '五行力量' in wuxing_info and wuxing_info['五行力量']:
                for wuxing, info in wuxing_info['五行力量'].items():
                    output += f"  {wuxing}：{info['力量值']} ({info['状态']}, {info['季节状态']})\n"
            else:
                # 从详细计算中提取
                final_scores = wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['最终得分']
                status_judgments = wuxing_info['计算详情']['五行力量详细计算']['第五步_最终力量汇总']['状态判定']

                for wuxing in ['木', '火', '土', '金', '水']:
                    score_str = final_scores[wuxing]
                    score = float(score_str.replace('分', ''))
                    status_str = status_judgments[wuxing]
                    status = status_str.split(' = ')[1] if ' = ' in status_str else "中和"

                    # 根据分数确定季节状态 - 使用config配置的阈值
                    season_thresholds = self.config.get('季节状态判断阈值', {
                        '得令': 3.0,
                        '失令': 1.0
                    })

                    if score >= season_thresholds['得令']:
                        season_status = "得令"
                    elif score <= season_thresholds['失令']:
                        season_status = "失令"
                    else:
                        season_status = "平和"

                    output += f"  {wuxing}：{score} ({status}, {season_status})\n"
        except Exception as e:
            # 如果都失败了，显示错误信息
            output += f"  五行力量数据获取失败: {e}\n"
            output += f"  可用数据键: {list(wuxing_info.keys())}\n"

        # 显示传统综合评分法的结果
        if body_analysis.get('method') == 'traditional':
            traditional_details = body_analysis['details']
            output += f"""
【十神分析】
日主：{basic_info['四柱']['日柱'][0]}
身强身弱：{body_analysis['strength']} (传统综合评分法: {body_analysis['total_score']:.2f}分/50分)
  - 得令: {traditional_details['得令']['权重后得分']:.1f}分 ({traditional_details['得令']['得令状态']})
  - 得地: {traditional_details['得地']['权重后得分']:.1f}分
  - 得势: {traditional_details['得势']['权重后得分']:.1f}分
  - 得生: {traditional_details['得生']['权重后得分']:.1f}分

十神权重分布：
"""
        else:
            # 兼容旧格式（如果还有的话）
            body_strength, support_power, drain_power = body_analysis
            output += f"""
【十神分析】
日主：{basic_info['四柱']['日柱'][0]}
身强身弱：{body_strength} (生扶力量: {support_power:.2f}, 克泄耗力量: {drain_power:.2f})

十神权重分布：
"""
        for shishen, weight in shishen_info['十神权重'].items():
            output += f"  {shishen}：{weight:.2f}\n"

        output += f"""
喜用神："""
        for shishen, weight in xiyong_list[:4]:
            output += f" {shishen}({weight:.2f})"

        output += f"""
忌神："""
        for shishen, weight in ji_list[:4]:
            output += f" {shishen}({weight:.2f})"

        # 添加排盘信息（简化版）
        output += f"""

【排盘信息】
"""
        # 按照图片中的顺序显示：年柱、月柱、日柱、时柱
        pillar_names = ['年柱', '月柱', '日柱', '时柱']
        pillar_display = ['年柱', '月柱', '日柱', '时柱']

        # 表格标题行
        output += "        "
        for display_name in pillar_display:
            output += f"{display_name:>8}"
        output += "\n"

        # 干支行
        output += "干支    "
        for pillar_name in pillar_names:
            gan, zhi = paipan_info[pillar_name]['干支']
            output += f"{gan}{zhi:>7}"
        output += "\n"

        # 主星行：日柱显示主星，其他柱显示天干对应的十神
        output += "主星    "
        for pillar_name in pillar_names:
            main_star = paipan_info[pillar_name]['主星']
            if main_star:
                output += f"{main_star:>8}"
            else:
                output += f"{'':>8}"
        output += "\n"

        # 星运行
        output += "星运    "
        for pillar_name in pillar_names:
            xingyun = paipan_info[pillar_name]['星运']
            output += f"{xingyun:>8}"
        output += "\n"

        # 自坐行（仅日柱）
        output += "自坐    "
        for pillar_name in pillar_names:
            if pillar_name == '日柱' and paipan_info[pillar_name]['自坐']:
                output += f"{paipan_info[pillar_name]['自坐']:>8}"
            else:
                output += f"{'':>8}"
        output += "\n"

        # 纳音行
        output += "纳音    "
        for pillar_name in pillar_names:
            nayin = paipan_info[pillar_name]['纳音']
            # 纳音名称较长，取前4个字符
            nayin_short = nayin[:4] if len(nayin) > 4 else nayin
            output += f"{nayin_short:>8}"
        output += "\n"

        # 空亡行：每个柱位分别显示空亡
        output += "空亡    "
        for pillar_name in pillar_names:
            kongwang_list = paipan_info[pillar_name]['空亡']
            if kongwang_list:
                kongwang_str = '、'.join(kongwang_list)
                # 如果空亡字符串太长，只显示第一个
                if len(kongwang_str) > 6:
                    kongwang_str = kongwang_list[0] + '等'
                output += f"{kongwang_str:>8}"
            else:
                output += f"{'无':>8}"
        output += "\n"

        # 神煞行：每个柱位分别显示神煞
        output += "神煞    "
        for pillar_name in pillar_names:
            shensha_list = paipan_info[pillar_name].get('神煞', [])
            if shensha_list:
                shensha_str = '、'.join(shensha_list)
                # 如果神煞字符串太长，只显示前几个
                if len(shensha_str) > 6:
                    # 计算能显示几个神煞
                    display_shensha = []
                    current_length = 0
                    for shensha in shensha_list:
                        if current_length + len(shensha) + 1 <= 6:  # +1 for 、
                            display_shensha.append(shensha)
                            current_length += len(shensha) + 1
                        else:
                            break
                    if len(display_shensha) < len(shensha_list):
                        shensha_str = '、'.join(display_shensha) + '等'
                    else:
                        shensha_str = '、'.join(display_shensha)
                output += f"{shensha_str:>8}"
            else:
                output += f"{'无':>8}"
        output += "\n"

        output += f"""

夫妻宫：{shishen_info['夫妻宫配偶星']['夫妻宫']}
配偶星：{shishen_info['夫妻宫配偶星']['配偶星']}

【大运流年分析】
起运：{dayun_info['起运信息']['起运方向']} {dayun_info['起运信息']['起运岁数']}岁{dayun_info['起运信息']['起运月份']}个月

大运序列：
"""

        for dayun, (start_age, end_age) in dayun_info['大运序列']:
            output += f"  {start_age}-{end_age}岁：{dayun}运\n"

        liunian = dayun_info['流年分析']
        liuyue = dayun_info['流月分析']
        liuri = dayun_info['流日分析']

        output += f"""
流年分析：{liunian['流年']} ({liunian['流年干支']})
  吉凶等级：{liunian['吉凶等级']} (得分: {liunian['总得分']})
  运势解读：{liunian['运势解读']}

流月分析：{liuyue['流月']} ({liuyue['流月干支']})
  得分：{liuyue['总得分']}

流日分析：{liuri['流日']} ({liuri['流日干支']})
  得分：{liuri['总得分']}

【合婚分析】
{result['合婚分析']['状态']}
{result['合婚分析']['说明']}

{'='*50}
分析完成
{'='*50}
"""

        return output

    def get_api_result(self, result):
        """获取API格式的结果（为前端接口预留）"""
        if "错误" in result:
            return {"success": False, "error": result["错误"]}

        return {
            "success": True,
            "data": {
                "basic_info": result["基本信息"],
                "wuxing_analysis": result["五行分析"],
                "shishen_analysis": result["十神分析"],
                "paipan_info": result.get("排盘信息", {}),
                "dayun_analysis": result["大运流年"],
                "marriage_analysis": result["合婚分析"]
            }
        }


# 测试函数
def test_comprehensive_analyzer():
    """测试综合分析器"""
    analyzer = ComprehensiveAnalyzer()
    
    # 进行完整分析
    result = analyzer.analyze_complete_bazi(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="广州",
        name="测试用户",
        gender="男"
    )
    
    # 输出格式化结果
    formatted_output = analyzer.format_comprehensive_output(result)
    print(formatted_output)
    
    return result


if __name__ == "__main__":
    test_comprehensive_analyzer()
