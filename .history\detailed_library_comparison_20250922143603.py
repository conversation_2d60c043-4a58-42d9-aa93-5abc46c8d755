#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
详细的八字库对比测试
对比我们的系统与其他Python八字库的详细输出
"""

import sys
from main import BaziSystem

def test_our_system():
    """测试我们的系统"""
    print("🎯 我们的系统排盘结果：")
    print("=" * 60)
    
    system = BaziSystem()
    
    # 测试数据：阳历1999年10月16日16:28分在重庆出生的女
    user_info = {
        'name': '详细对比测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )

        # 提取排盘信息
        paipan_info = result.get('排盘信息', {})
        
        print("\n📊 详细排盘信息：")
        print("-" * 40)
        
        # 显示各柱的详细信息
        for pillar_name in ['年柱', '月柱', '日柱', '时柱']:
            if pillar_name in paipan_info:
                pillar_data = paipan_info[pillar_name]
                
                print(f"\n{pillar_name}:")
                print(f"  天干: {pillar_data['干支'][0]}")
                print(f"  地支: {pillar_data['干支'][1]}")
                print(f"  主星: {pillar_data.get('主星', '无')}")
                print(f"  副星: {pillar_data.get('副星', '无')}")
                print(f"  星运: {pillar_data.get('星运', '无')}")
                print(f"  自坐: {pillar_data.get('自坐', '无')}")
                print(f"  空亡: {', '.join(pillar_data.get('空亡', []))}")
                print(f"  纳音: {pillar_data.get('纳音', '无')}")
                
                shensha = pillar_data.get('神煞', [])
                if shensha:
                    print(f"  神煞: {', '.join(shensha)}")
                else:
                    print(f"  神煞: 无")
        
        return True
        
    except Exception as e:
        print(f"❌ 我们的系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lunar_python():
    """测试lunar_python库"""
    print("\n🌙 lunar_python库排盘结果：")
    print("=" * 60)
    
    try:
        from lunar_python import Solar, Lunar
        
        # 创建阳历日期
        solar = Solar.fromYmdHms(1999, 10, 16, 16, 28, 0)
        lunar = solar.getLunar()
        
        # 获取八字
        bazi = lunar.getEightChar()
        
        print(f"\n📅 基本信息：")
        print(f"阳历: {solar.toYmd()}")
        print(f"阴历: {lunar.toString()}")
        
        print(f"\n🎯 四柱八字：")
        print(f"年柱: {bazi.getYear()}")
        print(f"月柱: {bazi.getMonth()}")
        print(f"日柱: {bazi.getDay()}")
        print(f"时柱: {bazi.getTime()}")
        
        # 获取详细信息
        print(f"\n📊 详细信息：")
        print(f"年干: {bazi.getYearGan()}")
        print(f"年支: {bazi.getYearZhi()}")
        print(f"月干: {bazi.getMonthGan()}")
        print(f"月支: {bazi.getMonthZhi()}")
        print(f"日干: {bazi.getDayGan()}")
        print(f"日支: {bazi.getDayZhi()}")
        print(f"时干: {bazi.getTimeGan()}")
        print(f"时支: {bazi.getTimeZhi()}")
        
        # 获取纳音
        print(f"\n🎵 纳音：")
        print(f"年柱纳音: {bazi.getYearNaYin()}")
        print(f"月柱纳音: {bazi.getMonthNaYin()}")
        print(f"日柱纳音: {bazi.getDayNaYin()}")
        print(f"时柱纳音: {bazi.getTimeNaYin()}")
        
        # 获取空亡
        print(f"\n🕳️ 空亡：")
        kong_wang = bazi.getDayKong()
        print(f"日柱空亡: {kong_wang}")
        
        return True
        
    except ImportError:
        print("❌ lunar_python库未安装")
        return False
    except Exception as e:
        print(f"❌ lunar_python测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_zhdate():
    """测试zhdate库"""
    print("\n📅 zhdate库测试结果：")
    print("=" * 60)
    
    try:
        from zhdate import ZhDate
        import datetime
        
        # 创建日期
        date = datetime.date(1999, 10, 16)
        zh_date = ZhDate.from_datetime(date)
        
        print(f"阳历: {date}")
        print(f"阴历: {zh_date}")
        print(f"干支年: {zh_date.to_lunar().chinese_year}")
        print(f"干支月: {zh_date.to_lunar().chinese_month}")
        print(f"干支日: {zh_date.to_lunar().chinese_day}")
        
        return True
        
    except ImportError:
        print("❌ zhdate库未安装")
        return False
    except Exception as e:
        print(f"❌ zhdate测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 详细八字库对比测试")
    print("测试数据：阳历1999年10月16日16:28分在重庆出生的女")
    print("=" * 80)
    
    # 测试我们的系统
    our_result = test_our_system()
    
    # 测试lunar_python
    lunar_result = test_lunar_python()
    
    # 测试zhdate
    zhdate_result = test_zhdate()
    
    print("\n" + "=" * 80)
    print("📋 对比测试总结：")
    print("-" * 40)
    print(f"我们的系统: {'✅ 成功' if our_result else '❌ 失败'}")
    print(f"lunar_python: {'✅ 成功' if lunar_result else '❌ 失败'}")
    print(f"zhdate: {'✅ 成功' if zhdate_result else '❌ 失败'}")
    
    if our_result and lunar_result:
        print("\n🎉 我们的系统与lunar_python都能正常工作，可以进行详细对比！")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
