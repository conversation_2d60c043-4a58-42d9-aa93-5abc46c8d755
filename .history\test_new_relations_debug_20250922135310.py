#!/usr/bin/env python3
"""
调试新关系是否生效
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_relations():
    comp = ComprehensiveAnalyzer()
    
    # 测试数据
    sizhu = {
        '年柱': ('己', '卯'),
        '月柱': ('甲', '戌'),
        '日柱': ('辛', '丑'),
        '时柱': ('丙', '申')
    }
    
    birth_info = {
        '四柱': sizhu
    }
    
    print("=== 测试新的特殊关系判断 ===")
    
    # 测试天干比劫帮扶
    day_gan = '辛'
    liunian_gan = '甲'
    
    print(f"1. 天干比劫帮扶测试：")
    print(f"   流年干: {liunian_gan}, 日干: {day_gan}")
    is_same_help = comp._is_same_help(liunian_gan, day_gan, day_gan)
    print(f"   是否为天干比劫帮扶: {is_same_help}")
    
    # 测试印星生扶
    print(f"\n2. 印星生扶测试：")
    print(f"   流年干: {liunian_gan}, 日干: {day_gan}")
    is_generate_help = comp._is_generate_help(liunian_gan, day_gan, day_gan)
    print(f"   是否为印星生扶: {is_generate_help}")
    
    # 测试地支根气
    print(f"\n3. 地支根气测试：")
    liunian_zhi = '辰'
    for pos, zhi in sizhu.items():
        zhi_name = zhi[1]
        is_root = comp._is_root_support(liunian_zhi, zhi_name, birth_info)
        print(f"   流年支{liunian_zhi} vs {pos}{zhi_name}: {is_root}")
    
    print(f"\n=== 测试天干关系计算 ===")
    
    # 测试天干关系计算
    relation_result = comp._calculate_tiangan_relation(liunian_gan, day_gan, birth_info)
    print(f"流年干{liunian_gan} vs 日干{day_gan}: {relation_result}")
    
    print(f"\n=== 测试地支关系计算 ===")
    
    # 测试地支关系计算
    for pos, (gan, zhi) in sizhu.items():
        relation_result = comp._calculate_dizhi_relation(liunian_zhi, zhi, birth_info)
        print(f"流年支{liunian_zhi} vs {pos}{zhi}: {relation_result}")
    
    print(f"\n=== 检查配置权重 ===")
    print(f"天干比劫帮扶权重: {comp.config['地支关系影响系数'].get('天干比劫帮扶', '未配置')}")
    print(f"印星生扶权重: {comp.config['地支关系影响系数'].get('印星生扶', '未配置')}")
    print(f"地支根气权重: {comp.config['地支关系影响系数'].get('地支根气', '未配置')}")

if __name__ == "__main__":
    test_relations()
