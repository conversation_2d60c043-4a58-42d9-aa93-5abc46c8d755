#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证config参数真正生效的测试
"""

from main import BaziSystem
import json

def test_config_real_fix():
    """验证config参数真正生效"""
    print("=" * 60)
    print("验证config参数真正生效")
    print("=" * 60)
    
    # 读取当前config
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("📋 当前config.json中您最新修改的参数：")
    print("-" * 40)
    print(f"地支六冲: {config['地支关系影响系数']['地支六冲']} (您最新修改为1.6)")
    print(f"天干五合: {config['地支关系影响系数']['天干五合']} (您修改为3)")
    print(f"相生: {config['地支关系影响系数']['相生']} (您最新修改为1.2)")
    print(f"相克: {config['地支关系影响系数']['相克']} (您最新修改为0.9)")
    
    system = BaziSystem()
    
    user_info = {
        'name': 'config真正生效验证',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        liunian_details = dayun_info.get('流年计算详情', {})
        
        print(f"\n🎯 使用详细计算后的流年结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查详细计算过程
        if liunian_details and '第二步_流年与原局作用关系' in liunian_details:
            print(f"\n🔍 详细计算过程验证：")
            print("-" * 40)
            
            step2 = liunian_details['第二步_流年与原局作用关系']
            
            # 检查天干作用详情
            tiangan_zuoyong = step2.get('天干作用详情', [])
            config_applied = False
            
            for zuoyong in tiangan_zuoyong:
                relation = zuoyong.get('五行关系', '')
                formula = zuoyong.get('计算公式', '')
                
                if '天干五合' in relation:
                    print(f"天干五合: {formula}")
                    if str(config['地支关系影响系数']['天干五合']) in formula:
                        print(f"✅ 天干五合系数 {config['地支关系影响系数']['天干五合']} 正确应用")
                        config_applied = True
                    else:
                        print(f"❌ 天干五合系数未正确应用")
                
                elif '相生' in relation:
                    print(f"相生: {formula}")
                    if str(config['地支关系影响系数']['相生']) in formula:
                        print(f"✅ 相生系数 {config['地支关系影响系数']['相生']} 正确应用")
                        config_applied = True
                    else:
                        print(f"❌ 相生系数未正确应用")
                
                elif '相克' in relation:
                    print(f"相克: {formula}")
                    if str(config['地支关系影响系数']['相克']) in formula:
                        print(f"✅ 相克系数 {config['地支关系影响系数']['相克']} 正确应用")
                        config_applied = True
                    else:
                        print(f"❌ 相克系数未正确应用")
            
            # 检查地支作用详情
            dizhi_zuoyong = step2.get('地支作用详情', [])
            for zuoyong in dizhi_zuoyong:
                relation = zuoyong.get('五行关系', '')
                formula = zuoyong.get('计算公式', '')
                
                if '地支六冲' in relation:
                    print(f"地支六冲: {formula}")
                    if str(config['地支关系影响系数']['地支六冲']) in formula:
                        print(f"✅ 地支六冲系数 {config['地支关系影响系数']['地支六冲']} 正确应用")
                        config_applied = True
                    else:
                        print(f"❌ 地支六冲系数未正确应用")
            
            if config_applied:
                print(f"\n✅ config参数已经正确应用到详细计算中")
            else:
                print(f"\n❌ config参数未在详细计算中找到应用证据")
        
        else:
            print(f"\n❌ 未找到详细计算过程，可能仍在使用简化逻辑")
        
        # 保存结果
        with open('config真正生效验证_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(f"config参数验证结果：\n")
            f.write(f"地支六冲: {config['地支关系影响系数']['地支六冲']}\n")
            f.write(f"天干五合: {config['地支关系影响系数']['天干五合']}\n")
            f.write(f"相生: {config['地支关系影响系数']['相生']}\n")
            f.write(f"相克: {config['地支关系影响系数']['相克']}\n")
            f.write(f"\n流年分析结果：\n")
            f.write(f"流年：{liunian_result.get('流年', '未知')}\n")
            f.write(f"流年干支：{liunian_result.get('流年干支', '未知')}\n")
            f.write(f"总得分：{liunian_result.get('总得分', 0):.2f}\n")
            f.write(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}\n")
            f.write(f"运势解读：{liunian_result.get('运势解读', '未知')}\n")
        
        print('\n✅ config真正生效验证报告已生成：config真正生效验证_八字分析_19991016_结果.txt')
        
        # 最终验证
        print(f"\n📊 最终验证结果：")
        print("-" * 40)
        
        current_score = liunian_result.get('总得分', 0)
        print(f"当前流年总得分: {current_score:.2f}")
        
        if current_score != 0.00:
            print(f"✅ 得分已发生变化，说明您的config修改生效了！")
        else:
            print(f"❌ 得分仍为0.00，可能config修改未完全生效")
        
        print("\n" + "=" * 60)
        print("config真正生效验证测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_real_fix()
