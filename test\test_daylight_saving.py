"""
测试夏令时处理功能
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_daylight_saving():
    """测试夏令时处理"""
    analyzer = ComprehensiveAnalyzer()
    
    print("=== 夏令时处理测试 ===")
    
    # 测试案例1：1989年夏令时期间（应该减去1小时）
    print("\n1. 测试1989年夏令时期间：")
    result1 = analyzer.analyze_complete_bazi(
        birth_year=1989,
        birth_month=7,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="北京",
        name="夏令时测试1",
        gender="男"
    )
    
    basic_info1 = result1['基本信息']
    print(f"输入时间：{basic_info1['输入时间']}")
    print(f"北京时间：{basic_info1['北京时间']}")
    print(f"夏令时信息：{basic_info1['夏令时信息']['说明']}")
    if basic_info1['夏令时信息']['是否夏令时期间']:
        print(f"夏令时开始：{basic_info1['夏令时信息']['夏令时开始']}")
        print(f"夏令时结束：{basic_info1['夏令时信息']['夏令时结束']}")
    
    # 测试案例2：1989年非夏令时期间（不应该调整）
    print("\n2. 测试1989年非夏令时期间：")
    result2 = analyzer.analyze_complete_bazi(
        birth_year=1989,
        birth_month=2,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="北京",
        name="夏令时测试2",
        gender="男"
    )
    
    basic_info2 = result2['基本信息']
    print(f"输入时间：{basic_info2['输入时间']}")
    print(f"北京时间：{basic_info2['北京时间']}")
    print(f"夏令时信息：{basic_info2['夏令时信息']['说明']}")
    
    # 测试案例3：1995年（不在夏令时实施期间）
    print("\n3. 测试1995年（不在夏令时实施期间）：")
    result3 = analyzer.analyze_complete_bazi(
        birth_year=1995,
        birth_month=7,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="北京",
        name="夏令时测试3",
        gender="男"
    )
    
    basic_info3 = result3['基本信息']
    print(f"输入时间：{basic_info3['输入时间']}")
    print(f"北京时间：{basic_info3['北京时间']}")
    print(f"夏令时信息：{basic_info3['夏令时信息']['说明']}")
    
    # 保存详细结果
    detailed_result = analyzer.format_comprehensive_output(result1, show_details=True)
    with open("夏令时测试_详细过程.txt", "w", encoding="utf-8") as f:
        f.write(detailed_result)
    
    print("\n详细结果已保存到: 夏令时测试_详细过程.txt")

if __name__ == "__main__":
    test_daylight_saving()
