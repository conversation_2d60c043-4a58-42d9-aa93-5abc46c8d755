"""
测试透干和月干支权重修改
"""

from calendar_converter import CalendarConverter
from wuxing_calculator import WuxingCalculator
from shishen_calculator import ShishenCalculator

def test_modifications():
    """测试修改后的计算逻辑"""
    
    # 测试用例：1991年5月25日15时，广州
    converter = CalendarConverter()
    result = converter.process_birth_info(
        birth_year=1991,
        birth_month=5,
        birth_day=25,
        birth_hour=15,
        birth_minute=0,
        city_name="广州",
        name="测试用户"
    )
    
    print("=== 四柱八字 ===")
    print(f"年柱：{result['四柱']['年柱'][0]}{result['四柱']['年柱'][1]}")
    print(f"月柱：{result['四柱']['月柱'][0]}{result['四柱']['月柱'][1]}")
    print(f"日柱：{result['四柱']['日柱'][0]}{result['四柱']['日柱'][1]}")
    print(f"时柱：{result['四柱']['时柱'][0]}{result['四柱']['时柱'][1]}")
    print()
    
    # 测试五行计算
    wuxing_calc = WuxingCalculator()
    wuxing_scores, season = wuxing_calc.calculate_wuxing_scores(result['四柱'], result['阴历信息'])
    
    print("=== 五行力量分析（修改后）===")
    print(f"当前季节：{season}")
    for wuxing, info in wuxing_scores.items():
        print(f"{wuxing}：{info['力量值']} ({info['状态']}, {info['季节状态']})")
    print()
    
    # 测试十神计算
    shishen_calc = ShishenCalculator()
    shishen_weights = shishen_calc.calculate_shishen_weights(result['四柱'])
    body_analysis = shishen_calc.analyze_body_strength(result['四柱'])
    
    print("=== 十神分析（修改后）===")
    print(f"日主：{result['四柱']['日柱'][0]} ({shishen_calc.tiangan_info[result['四柱']['日柱'][0]]['五行']}{shishen_calc.tiangan_info[result['四柱']['日柱'][0]]['阴阳']})")
    print()
    print("十神权重分布：")
    for shishen, weight in shishen_weights.items():
        print(f"{shishen}：{weight:.2f}")
    print()
    
    body_strength, support_power, drain_power = body_analysis
    print(f"身强身弱判断：{body_strength}")
    print(f"生扶力量：{support_power:.2f}")
    print(f"克泄耗力量：{drain_power:.2f}")
    print()
    
    # 检查透干情况
    day_gan = result['四柱']['日柱'][0]
    print("=== 透干检查 ===")
    print(f"日柱天干：{day_gan}")
    
    # 检查各地支藏干
    dizhi_info = {
        '子': {'癸': 1.0},
        '丑': {'己': 0.7, '癸': 0.2, '辛': 0.1},
        '寅': {'甲': 0.6, '丙': 0.3, '戊': 0.1},
        '卯': {'乙': 1.0},
        '辰': {'戊': 0.6, '乙': 0.3, '癸': 0.1},
        '巳': {'丙': 0.6, '戊': 0.3, '庚': 0.1},
        '午': {'丁': 0.7, '己': 0.3},
        '未': {'己': 0.6, '丁': 0.3, '乙': 0.1},
        '申': {'庚': 0.6, '壬': 0.3, '戊': 0.1},
        '酉': {'辛': 1.0},
        '戌': {'戊': 0.6, '辛': 0.3, '丁': 0.1},
        '亥': {'壬': 0.7, '甲': 0.3}
    }
    
    positions = [('年支', result['四柱']['年柱'][1]), ('月支', result['四柱']['月柱'][1]), 
                ('日支', result['四柱']['日柱'][1]), ('时支', result['四柱']['时柱'][1])]
    
    for pos_name, zhi in positions:
        canggan_dict = dizhi_info[zhi]
        print(f"{pos_name}({zhi})藏干：", end="")
        for gan, weight in canggan_dict.items():
            if gan == day_gan:
                print(f" {gan}(透干,权重{weight})", end="")
            else:
                print(f" {gan}(权重{weight})", end="")
        print()
    
    print()
    print("=== 月干支权重提升检查 ===")
    print(f"月干：{result['四柱']['月柱'][0]} (权重应为 1×3=3)")
    print(f"月支：{result['四柱']['月柱'][1]} (本气和藏干权重都应乘以3)")

if __name__ == "__main__":
    test_modifications()
