#!/usr/bin/env python3
"""
测试神煞计算
"""

from paipan_calculator import PaipanCalculator

def test_shensha():
    calc = PaipanCalculator()

    # 测试数据：己卯 甲戌 辛丑 丙申
    sizhu = {
        '年柱': ('己', '卯'),
        '月柱': ('甲', '戌'),
        '日柱': ('辛', '丑'),
        '时柱': ('丙', '申')
    }

    print('测试神煞计算：')
    shensha_result = calc.calculate_shensha(sizhu, {})

    for pillar, shensha_list in shensha_result.items():
        print(f'{pillar}: {shensha_list if shensha_list else "无"}')

    print()
    print('检查神煞表：')
    print('己对应的天乙贵人:', calc.shensha_table['天乙贵人'].get('己', '无'))
    print('辛对应的天乙贵人:', calc.shensha_table['天乙贵人'].get('辛', '无'))
    print('己对应的太极贵人:', calc.shensha_table['太极贵人'].get('己', '无'))
    print('辛对应的太极贵人:', calc.shensha_table['太极贵人'].get('辛', '无'))
    
    print()
    print('详细检查：')
    year_gan, year_zhi = sizhu['年柱']
    day_gan, day_zhi = sizhu['日柱']
    
    print(f'年干: {year_gan}, 日干: {day_gan}')
    
    # 检查月柱戌
    print(f'月柱戌是否在己的太极贵人中: {"戌" in calc.shensha_table["太极贵人"]["己"]}')
    print(f'月柱戌是否在辛的太极贵人中: {"戌" in calc.shensha_table["太极贵人"].get("辛", [])}')
    
    # 检查日柱丑
    print(f'日柱丑是否在己的太极贵人中: {"丑" in calc.shensha_table["太极贵人"]["己"]}')
    print(f'日柱丑是否在辛的太极贵人中: {"丑" in calc.shensha_table["太极贵人"].get("辛", [])}')
    
    # 检查时柱申
    print(f'时柱申是否在己的天乙贵人中: {"申" in calc.shensha_table["天乙贵人"]["己"]}')
    print(f'时柱申是否在辛的天乙贵人中: {"申" in calc.shensha_table["天乙贵人"].get("辛", [])}')

if __name__ == "__main__":
    test_shensha()
