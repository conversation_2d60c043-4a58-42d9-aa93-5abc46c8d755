#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的排盘格式
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_new_paipan_format():
    """测试新的排盘格式"""
    # 创建综合分析实例
    comp = ComprehensiveAnalyzer()
    
    # 用户信息
    user_info = {
        'name': '新排盘格式测试',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'city_name': '重庆',
        'gender': '女'
    }
    
    try:
        # 进行八字分析
        result = comp.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['city_name'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        if "错误" in result:
            print(f"分析失败：{result['错误']}")
            return False
        
        # 生成文件名
        base_filename = f"{user_info['name']}_八字分析_{user_info['birth_year']}{user_info['birth_month']:02d}{user_info['birth_day']:02d}"
        
        # 保存简洁版结果
        simple_filename = f"{base_filename}_简单报告.txt"
        simple_result = comp.format_simple_output(result)
        with open(simple_filename, 'w', encoding='utf-8') as f:
            f.write(simple_result)
        
        # 保存详细版结果
        detailed_filename = f"{base_filename}_详细报告.txt"
        detailed_result = comp.format_comprehensive_output(result, show_details=True)
        with open(detailed_filename, 'w', encoding='utf-8') as f:
            f.write(detailed_result)
        
        print(f"分析结果已保存到:")
        print(f"  简洁版: {simple_filename}")
        print(f"  详细版: {detailed_filename}")
        
        return True
        
    except Exception as e:
        print(f"生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_new_paipan_format()
