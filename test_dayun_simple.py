"""
简单测试流年流月流日计算
"""

from dayun_calculator import DayunCalculator
from calendar_converter import CalendarConverter
from shishen_calculator import ShishenCalculator

def test_simple():
    """简单测试"""
    # 获取基础信息
    converter = CalendarConverter()
    birth_result = converter.process_birth_info(
        birth_year=1990, birth_month=5, birth_day=15,
        birth_hour=14, birth_minute=30, city_name="广州", name="测试用户"
    )
    
    print("基础信息获取成功")
    print(f"四柱: {birth_result['四柱']}")
    
    # 十神分析
    shishen_calc = ShishenCalculator()
    shishen_weights = shishen_calc.calculate_shishen_weights(birth_result['四柱'])
    body_analysis = shishen_calc.analyze_body_strength(birth_result['四柱'])
    xiyong_info = shishen_calc.determine_xiyongshen(body_analysis['strength'], shishen_weights)
    
    print("十神分析完成")
    print(f"身强身弱: {body_analysis['strength']}")
    
    # 大运分析
    calculator = DayunCalculator()
    qiyun_info = calculator.calculate_qiyun_time(birth_result, '男')
    dayun_list = calculator.generate_dayun_sequence(
        birth_result['四柱']['月柱'], qiyun_info['起运方向']
    )
    dayun_ages = calculator.calculate_dayun_ages(qiyun_info)
    
    print("大运计算完成")
    print(f"起运信息: {qiyun_info}")
    
    # 流年流月流日分析
    target_date = {'年': 2025, '月': 10, '日': 1}
    
    print("\n开始流年流月流日分析...")
    
    try:
        liunian_result = calculator.analyze_liunian(birth_result, dayun_list[0], xiyong_info, target_date)
        print(f"流年分析成功: {liunian_result}")
        
        liuyue_result = calculator.analyze_liuyue(liunian_result, target_date, birth_result, xiyong_info)
        print(f"流月分析成功: {liuyue_result}")
        
        liuri_result = calculator.analyze_liuri(liunian_result, liuyue_result, target_date, birth_result, xiyong_info)
        print(f"流日分析成功: {liuri_result}")
        
        print("\n=== 所有计算成功完成 ===")
        
    except Exception as e:
        print(f"计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple()
