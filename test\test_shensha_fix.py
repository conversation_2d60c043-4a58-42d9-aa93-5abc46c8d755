#!/usr/bin/env python3
"""
测试修复后的神煞计算
"""

from paipan_calculator import PaipanCalculator

def test_shensha():
    calc = PaipanCalculator()
    
    # 测试数据：己卯 甲戌 辛丑 丙申
    sizhu = {
        '年柱': ('己', '卯'),
        '月柱': ('甲', '戌'),
        '日柱': ('辛', '丑'),
        '时柱': ('丙', '申')
    }
    
    shishen_info = {'十神权重': {}, '身强身弱': {'strength': '身强'}}
    
    print("=== 测试修复后的神煞计算 ===")
    
    # 计算神煞
    shensha_result = calc.calculate_shensha(sizhu, shishen_info)
    
    print("神煞计算结果：")
    for pillar_name in ['年柱', '月柱', '日柱', '时柱']:
        shensha_list = shensha_result[pillar_name]
        if shensha_list:
            print(f"{pillar_name}: {', '.join(shensha_list)}")
        else:
            print(f"{pillar_name}: 无")
    
    print("\n=== 检查神煞表内容 ===")
    print("天乙贵人表：")
    for gan, zhis in calc.shensha_table['天乙贵人'].items():
        print(f"  {gan}: {zhis}")
    
    print("\n太极贵人表：")
    for gan, zhis in calc.shensha_table['太极贵人'].items():
        print(f"  {gan}: {zhis}")
    
    print("\n文昌贵人表：")
    for gan, zhis in calc.shensha_table['文昌贵人'].items():
        print(f"  {gan}: {zhis}")

if __name__ == "__main__":
    test_shensha()
