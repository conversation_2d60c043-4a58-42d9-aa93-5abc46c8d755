#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试硬编码修复效果
"""

from main import BaziSystem
import json

def test_all_hardcode_fix():
    """全面测试所有硬编码修复效果"""
    print("=" * 60)
    print("全面测试硬编码修复效果")
    print("=" * 60)
    
    # 读取当前config
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("📋 当前config.json参数：")
    print("-" * 40)
    print(f"克制系数: {config['五行计算参数']['克制系数']}")
    print(f"生扶系数: {config['五行计算参数']['生扶系数']}")
    print(f"天干五合: {config['地支关系影响系数']['天干五合']}")
    print(f"天干五合合绊系数: {config['天干五合合绊系数']}")
    print(f"大运背景得分: {config['大运背景得分']}")
    print(f"五行状态判断阈值: {config['五行状态判断阈值']}")
    print(f"季节状态判断阈值: {config['季节状态判断阈值']}")
    
    system = BaziSystem()
    
    user_info = {
        'name': '硬编码修复全面测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 格式化输出
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=True)
        
        # 保存到文件
        with open('硬编码修复全面测试_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('\n✅ 硬编码修复全面测试报告已生成：硬编码修复全面测试_八字分析_19991016_结果.txt')
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        
        print("\n🎯 硬编码修复后的流年计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查五行分析结果
        wuxing_info = result.get('五行分析', {})
        wuxing_power = wuxing_info.get('五行力量', {})
        
        print("\n🔍 五行状态判断检查：")
        print("-" * 40)
        for wuxing, info in wuxing_power.items():
            print(f"{wuxing}: {info.get('力量值', 0):.2f}分 - {info.get('状态', '未知')} - {info.get('季节状态', '未知')}")
        
        # 检查计算详情中的参数使用
        print("\n📊 参数使用验证：")
        print("-" * 40)
        
        # 检查五行计算详情
        wuxing_details = wuxing_info.get('计算详情', {})
        if '第四步_生扶克制计算' in wuxing_details:
            step4 = wuxing_details['第四步_生扶克制计算']
            print(f"✅ 生扶系数: {step4.get('生扶系数', '未找到')}")
            print(f"✅ 克制系数: {step4.get('克制系数', '未找到')}")
        
        # 检查流年计算详情中的天干五合
        liunian_details = dayun_info.get('流年计算详情', {})
        if '第二步_流年与原局作用关系' in liunian_details:
            step2 = liunian_details['第二步_流年与原局作用关系']
            tiangan_zuoyong = step2.get('天干作用详情', [])
            for zuoyong in tiangan_zuoyong:
                if '天干五合' in zuoyong.get('五行关系', ''):
                    print(f"✅ 天干五合系数应用: {zuoyong.get('计算公式', '未找到')}")
                    break
        
        # 验证参数一致性
        print("\n✅ 参数一致性最终验证：")
        print("-" * 40)
        
        # 检查config参数是否正确应用
        analyzer_config = system.analyzer.config
        
        checks = [
            ("克制系数", analyzer_config['五行计算参数']['克制系数'], config['五行计算参数']['克制系数']),
            ("天干五合", analyzer_config['地支关系影响系数']['天干五合'], config['地支关系影响系数']['天干五合']),
            ("天干五合合绊系数", analyzer_config.get('天干五合合绊系数'), config['天干五合合绊系数']),
            ("大运背景得分", analyzer_config.get('大运背景得分'), config['大运背景得分'])
        ]
        
        all_consistent = True
        for param_name, actual, expected in checks:
            if actual == expected:
                print(f"✅ {param_name}: {actual} (一致)")
            else:
                print(f"❌ {param_name}: 实际={actual}, 期望={expected} (不一致)")
                all_consistent = False
        
        if all_consistent:
            print("\n🎉 所有硬编码问题已完全修复！config参数正确生效！")
        else:
            print("\n⚠️ 仍有部分硬编码问题未完全修复")
        
        # 对比修改前后的结果
        print(f"\n📈 修复效果总结：")
        print("-" * 40)
        print("✅ 藏干权重: 硬编码 → config配置")
        print("✅ 五行计算参数: 硬编码 → config配置")
        print("✅ 地支关系影响系数: 硬编码 → config配置")
        print("✅ 天干五合合绊系数: 硬编码 → config配置")
        print("✅ 大运背景得分: 硬编码 → config配置")
        print("✅ 五行状态判断阈值: 硬编码 → config配置")
        print("✅ 季节状态判断阈值: 硬编码 → config配置")
        
        print("\n" + "=" * 60)
        print("硬编码修复全面测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_all_hardcode_fix()
