@echo off
echo 正在准备上传到 GitHub...

REM 创建新的文件夹用于上传
mkdir calculation_upload
cd calculation_upload

REM 初始化 Git 仓库
git init

REM 添加远程仓库
git remote add origin https://github.com/suanlou/calculation.git

REM 复制核心文件
copy ..\main.py .
copy ..\calendar_converter.py .
copy ..\wuxing_calculator.py .
copy ..\shishen_calculator.py .
copy ..\dayun_calculator.py .
copy ..\paipan_calculator.py .
copy ..\comprehensive_result.py .
copy ..\config.json .
copy ..\city_coordinates.json .
copy ..\README_GITHUB.md README.md
copy ..\.gitignore .
copy ..\requirements.txt .

REM 添加所有文件
git add .

REM 提交
git commit -m "Initial commit: 八字占卜计算系统核心代码"

REM 推送到 GitHub
git branch -M main
git push -u origin main

echo 上传完成！
pause
