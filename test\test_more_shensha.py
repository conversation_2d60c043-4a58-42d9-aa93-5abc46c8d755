#!/usr/bin/env python3
"""
测试更多神煞类型
"""

from paipan_calculator import PaipanCalculator

def test_more_shensha():
    calc = PaipanCalculator()
    
    # 测试数据：己卯 甲戌 辛丑 丙申
    sizhu = {
        '年柱': ('己', '卯'),
        '月柱': ('甲', '戌'),
        '日柱': ('辛', '丑'),
        '时柱': ('丙', '申')
    }
    
    year_gan, year_zhi = sizhu['年柱']
    day_gan, day_zhi = sizhu['日柱']
    
    print("=== 手动检查各种神煞 ===")
    print(f"年干: {year_gan}, 日干: {day_gan}")
    print(f"地支: {[zhi for gan, zhi in sizhu.values()]}")
    
    # 检查驿马
    print("\n驿马检查：")
    yima_table = calc.shensha_table.get('驿马', {})
    for pillar_name, (gan, zhi) in sizhu.items():
        if zhi in yima_table:
            yima_zhi = yima_table[zhi]
            print(f"  {pillar_name}({zhi})的驿马: {yima_zhi}")
            # 检查其他柱是否有这个驿马
            for other_pillar, (other_gan, other_zhi) in sizhu.items():
                if other_zhi in yima_zhi:
                    print(f"    -> {other_pillar}有驿马{other_zhi}")
    
    # 检查华盖
    print("\n华盖检查：")
    huagai_table = calc.shensha_table.get('华盖', {})
    for pillar_name, (gan, zhi) in sizhu.items():
        if zhi in huagai_table:
            huagai_zhi = huagai_table[zhi]
            print(f"  {pillar_name}({zhi})的华盖: {huagai_zhi}")
            # 检查其他柱是否有这个华盖
            for other_pillar, (other_gan, other_zhi) in sizhu.items():
                if other_zhi in huagai_zhi:
                    print(f"    -> {other_pillar}有华盖{other_zhi}")
    
    # 检查桃花
    print("\n桃花检查：")
    taohua_table = calc.shensha_table.get('桃花', {})
    for pillar_name, (gan, zhi) in sizhu.items():
        if zhi in taohua_table:
            taohua_zhi = taohua_table[zhi]
            print(f"  {pillar_name}({zhi})的桃花: {taohua_zhi}")
            # 检查其他柱是否有这个桃花
            for other_pillar, (other_gan, other_zhi) in sizhu.items():
                if other_zhi in taohua_zhi:
                    print(f"    -> {other_pillar}有桃花{other_zhi}")
    
    print("\n=== 实际神煞计算结果 ===")
    shishen_info = {'十神权重': {}, '身强身弱': {'strength': '身强'}}
    shensha_result = calc.calculate_shensha(sizhu, shishen_info)
    
    for pillar_name in ['年柱', '月柱', '日柱', '时柱']:
        shensha_list = shensha_result[pillar_name]
        if shensha_list:
            print(f"{pillar_name}: {', '.join(shensha_list)}")
        else:
            print(f"{pillar_name}: 无")

if __name__ == "__main__":
    test_more_shensha()
