"""
八字排盘补充逻辑计算器
包含：主星、副星、星运、自坐、空亡、纳音、神煞的计算
"""

class PaipanCalculator:
    def __init__(self):
        # 十天干生旺死绝表
        self.shengwang_table = {
            '亥': {'甲': '长生', '丙': '绝', '戊': '绝', '庚': '病', '壬': '临官', '乙': '死', '丁': '胎', '己': '胎', '辛': '沐浴', '癸': '帝旺'},
            '子': {'甲': '沐浴', '丙': '胎', '戊': '胎', '庚': '死', '壬': '帝旺', '乙': '长生', '丁': '绝', '己': '绝', '辛': '长生', '癸': '临官'},
            '丑': {'甲': '冠带', '丙': '养', '戊': '养', '庚': '墓', '壬': '衰', '乙': '衰', '丁': '墓', '己': '墓', '辛': '养', '癸': '冠带'},
            '寅': {'甲': '临官', '丙': '长生', '戊': '长生', '庚': '绝', '壬': '病', '乙': '临官', '丁': '沐浴', '己': '沐浴', '辛': '绝', '癸': '衰'},
            '卯': {'甲': '帝旺', '丙': '沐浴', '戊': '沐浴', '庚': '胎', '壬': '死', '乙': '帝旺', '丁': '长生', '己': '长生', '辛': '胎', '癸': '病'},
            '辰': {'甲': '衰', '丙': '冠带', '戊': '冠带', '庚': '养', '壬': '墓', '乙': '衰', '丁': '冠带', '己': '冠带', '辛': '养', '癸': '墓'},
            '巳': {'甲': '病', '丙': '临官', '戊': '临官', '庚': '长生', '壬': '绝', '乙': '病', '丁': '临官', '己': '临官', '辛': '长生', '癸': '绝'},
            '午': {'甲': '死', '丙': '帝旺', '戊': '帝旺', '庚': '沐浴', '壬': '胎', '乙': '死', '丁': '帝旺', '己': '帝旺', '辛': '沐浴', '癸': '胎'},
            '未': {'甲': '墓', '丙': '衰', '戊': '衰', '庚': '冠带', '壬': '养', '乙': '墓', '丁': '衰', '己': '衰', '辛': '冠带', '癸': '养'},
            '申': {'甲': '绝', '丙': '病', '戊': '病', '庚': '临官', '壬': '长生', '乙': '绝', '丁': '病', '己': '病', '辛': '临官', '癸': '长生'},
            '酉': {'甲': '胎', '丙': '死', '戊': '死', '庚': '帝旺', '壬': '沐浴', '乙': '胎', '丁': '死', '己': '死', '辛': '帝旺', '癸': '沐浴'},
            '戌': {'甲': '养', '丙': '墓', '戊': '墓', '庚': '衰', '壬': '冠带', '乙': '养', '丁': '墓', '己': '墓', '辛': '衰', '癸': '冠带'}
        }
        
        # 六十甲子纳音表
        self.nayin_table = {
            '甲子': '海中金', '乙丑': '海中金', '丙寅': '炉中火', '丁卯': '炉中火', '戊辰': '大林木', '己巳': '大林木',
            '庚午': '路旁土', '辛未': '路旁土', '壬申': '剑锋金', '癸酉': '剑锋金', '甲戌': '山头火', '乙亥': '山头火',
            '丙子': '涧下水', '丁丑': '涧下水', '戊寅': '城头土', '己卯': '城头土', '庚辰': '白蜡金', '辛巳': '白蜡金',
            '壬午': '杨柳木', '癸未': '杨柳木', '甲申': '泉中水', '乙酉': '泉中水', '丙戌': '屋上土', '丁亥': '屋上土',
            '戊子': '霹雳火', '己丑': '霹雳火', '庚寅': '松柏木', '辛卯': '松柏木', '壬辰': '长流水', '癸巳': '长流水',
            '甲午': '沙中金', '乙未': '沙中金', '丙申': '山下火', '丁酉': '山下火', '戊戌': '平地木', '己亥': '平地木',
            '庚子': '壁上土', '辛丑': '壁上土', '壬寅': '金箔金', '癸卯': '金箔金', '甲辰': '覆灯火', '乙巳': '覆灯火',
            '丙午': '天河水', '丁未': '天河水', '戊申': '大驿土', '己酉': '大驿土', '庚戌': '钗钏金', '辛亥': '钗钏金',
            '壬子': '桑柘木', '癸丑': '桑柘木', '甲寅': '大溪水', '乙卯': '大溪水', '丙辰': '沙中土', '丁巳': '沙中土',
            '戊午': '天上火', '己未': '天上火', '庚申': '石榴木', '辛酉': '石榴木', '壬戌': '大海水', '癸亥': '大海水'
        }
        
        # 空亡（旬空）表
        self.kongwang_table = {
            # 甲子旬
            '甲子': ['戌', '亥'], '乙丑': ['戌', '亥'], '丙寅': ['戌', '亥'], '丁卯': ['戌', '亥'], '戊辰': ['戌', '亥'],
            '己巳': ['戌', '亥'], '庚午': ['戌', '亥'], '辛未': ['戌', '亥'], '壬申': ['戌', '亥'], '癸酉': ['戌', '亥'],
            # 甲戌旬
            '甲戌': ['申', '酉'], '乙亥': ['申', '酉'], '丙子': ['申', '酉'], '丁丑': ['申', '酉'], '戊寅': ['申', '酉'],
            '己卯': ['申', '酉'], '庚辰': ['申', '酉'], '辛巳': ['申', '酉'], '壬午': ['申', '酉'], '癸未': ['申', '酉'],
            # 甲申旬
            '甲申': ['午', '未'], '乙酉': ['午', '未'], '丙戌': ['午', '未'], '丁亥': ['午', '未'], '戊子': ['午', '未'],
            '己丑': ['午', '未'], '庚寅': ['午', '未'], '辛卯': ['午', '未'], '壬辰': ['午', '未'], '癸巳': ['午', '未'],
            # 甲午旬
            '甲午': ['辰', '巳'], '乙未': ['辰', '巳'], '丙申': ['辰', '巳'], '丁酉': ['辰', '巳'], '戊戌': ['辰', '巳'],
            '己亥': ['辰', '巳'], '庚子': ['辰', '巳'], '辛丑': ['辰', '巳'], '壬寅': ['辰', '巳'], '癸卯': ['辰', '巳'],
            # 甲辰旬
            '甲辰': ['寅', '卯'], '乙巳': ['寅', '卯'], '丙午': ['寅', '卯'], '丁未': ['寅', '卯'], '戊申': ['寅', '卯'],
            '己酉': ['寅', '卯'], '庚戌': ['寅', '卯'], '辛亥': ['寅', '卯'], '壬子': ['寅', '卯'], '癸丑': ['寅', '卯'],
            # 甲寅旬
            '甲寅': ['子', '丑'], '乙卯': ['子', '丑'], '丙辰': ['子', '丑'], '丁巳': ['子', '丑'], '戊午': ['子', '丑'],
            '己未': ['子', '丑'], '庚申': ['子', '丑'], '辛酉': ['子', '丑'], '壬戌': ['子', '丑'], '癸亥': ['子', '丑']
        }
        
        # 神煞查询表
        self.shensha_table = {
            '天乙贵人': {
                '甲': ['丑', '未'], '乙': ['子', '申'], '丙': ['亥', '酉'], '丁': ['亥', '酉'], '戊': ['丑', '未'],
                '己': ['子', '申'], '庚': ['丑', '未'], '辛': ['寅', '午'], '壬': ['卯', '巳'], '癸': ['卯', '巳']
            },
            '太极贵人': {
                '甲': ['子', '午'], '乙': ['子', '午'], '丙': ['卯', '酉'], '丁': ['卯', '酉'], '戊': ['辰', '戌', '丑', '未'],
                '己': ['辰', '戌', '丑', '未'], '庚': ['寅', '亥'], '辛': ['寅', '亥'], '壬': ['巳', '申'], '癸': ['巳', '申']
            },
            '文昌贵人': {
                '甲': ['巳'], '乙': ['午'], '丙': ['申'], '丁': ['酉'], '戊': ['申'],
                '己': ['酉'], '庚': ['亥'], '辛': ['子'], '壬': ['寅'], '癸': ['卯']
            },
            '桃花': {
                '子': ['酉'], '丑': ['午'], '寅': ['卯'], '卯': ['子'], '辰': ['酉'], '巳': ['午'],
                '午': ['卯'], '未': ['子'], '申': ['酉'], '酉': ['午'], '戌': ['卯'], '亥': ['子']
            },
            '驿马': {
                '申': ['寅'], '子': ['寅'], '辰': ['寅'],  # 申子辰见寅
                '亥': ['巳'], '卯': ['巳'], '未': ['巳'],  # 亥卯未见巳
                '寅': ['申'], '午': ['申'], '戌': ['申'],  # 寅午戌见申
                '巳': ['亥'], '酉': ['亥'], '丑': ['亥']   # 巳酉丑见亥
            },
            '华盖': {
                '申': ['辰'], '子': ['辰'], '辰': ['辰'],  # 申子辰见辰
                '亥': ['未'], '卯': ['未'], '未': ['未'],  # 亥卯未见未
                '寅': ['戌'], '午': ['戌'], '戌': ['戌'],  # 寅午戌见戌
                '巳': ['丑'], '酉': ['丑'], '丑': ['丑']   # 巳酉丑见丑
            },
            '孤辰': {
                '亥': ['寅'], '子': ['寅'], '丑': ['寅'],  # 亥子丑人见寅
                '寅': ['巳'], '卯': ['巳'], '辰': ['巳'],  # 寅卯辰人见巳
                '巳': ['申'], '午': ['申'], '未': ['申'],  # 巳午未人见申
                '申': ['亥'], '酉': ['亥'], '戌': ['亥']   # 申酉戌人见亥
            },
            '寡宿': {
                '亥': ['戌'], '子': ['戌'], '丑': ['戌'],  # 亥子丑人见戌
                '寅': ['丑'], '卯': ['丑'], '辰': ['丑'],  # 寅卯辰人见丑
                '巳': ['辰'], '午': ['辰'], '未': ['辰'],  # 巳午未人见辰
                '申': ['未'], '酉': ['未'], '戌': ['未']   # 申酉戌人见未
            }
        }
        
        # 特殊神煞
        self.special_shensha = {
            '阴阳差错': ['丙子', '丁丑', '戊寅', '辛卯', '壬辰', '癸巳', '丙午', '丁未', '戊申', '辛酉', '壬戌', '癸亥'],
            '十恶大败': ['甲辰', '乙巳', '壬申', '丙申', '丁亥', '庚辰', '戊戌', '癸亥', '辛巳', '己丑']
        }

    def calculate_main_star(self, day_gan):
        """计算日柱主星"""
        yang_gan = ['甲', '丙', '戊', '庚', '壬']
        return '元男' if day_gan in yang_gan else '元女'

    def calculate_star_fortune(self, gan, zhi):
        """计算星运（长生十二宫）"""
        return self.shengwang_table.get(zhi, {}).get(gan, '未知')

    def calculate_self_sitting(self, day_gan, day_zhi):
        """计算自坐（仅日柱）"""
        return self.calculate_star_fortune(day_gan, day_zhi)

    def calculate_kongwang(self, day_pillar):
        """计算空亡"""
        return self.kongwang_table.get(day_pillar, [])

    def calculate_nayin(self, gan_zhi):
        """计算纳音"""
        return self.nayin_table.get(gan_zhi, '未知')

    def calculate_shensha(self, sizhu, shishen_info):
        """计算神煞"""
        result = {'年柱': [], '月柱': [], '日柱': [], '时柱': []}
        
        # 获取各柱信息
        year_gan, year_zhi = sizhu['年柱']
        month_gan, month_zhi = sizhu['月柱']
        day_gan, day_zhi = sizhu['日柱']
        hour_gan, hour_zhi = sizhu['时柱']
        
        # 计算主类型神煞 - 遍历所有神煞类型
        for pillar_name, (gan, zhi) in sizhu.items():
            pillar_shensha = []

            # 1. 以天干为准的神煞（天乙贵人、太极贵人、文昌贵人）
            tiangan_shensha_types = ['天乙贵人', '太极贵人', '文昌贵人']
            for shensha_type in tiangan_shensha_types:
                if shensha_type in self.shensha_table:
                    # 以年干和日干为准查找
                    for ref_gan in [year_gan, day_gan]:
                        if ref_gan in self.shensha_table[shensha_type]:
                            if zhi in self.shensha_table[shensha_type][ref_gan]:
                                if shensha_type not in pillar_shensha:  # 避免重复
                                    pillar_shensha.append(shensha_type)
                                break  # 找到一个就跳出ref_gan循环，但继续下一个shensha_type

            # 2. 以地支为准的神煞（驿马、华盖、桃花、孤辰、寡宿）
            dizhi_shensha_types = ['驿马', '华盖', '桃花', '孤辰', '寡宿']
            for shensha_type in dizhi_shensha_types:
                if shensha_type in self.shensha_table:
                    # 检查当前地支是否能产生这种神煞
                    if zhi in self.shensha_table[shensha_type]:
                        target_zhis = self.shensha_table[shensha_type][zhi]
                        # 检查四柱中是否有目标地支
                        for target_zhi in target_zhis:
                            for check_pillar, (check_gan, check_zhi) in sizhu.items():
                                if check_zhi == target_zhi:
                                    if shensha_type not in pillar_shensha:
                                        pillar_shensha.append(shensha_type)
                                    break
                            if shensha_type in pillar_shensha:
                                break

            result[pillar_name] = pillar_shensha
        
        # 特殊神煞（仅日柱）
        day_pillar = day_gan + day_zhi
        if day_pillar in self.special_shensha['阴阳差错']:
            result['日柱'].append('阴阳差错')
        if day_pillar in self.special_shensha['十恶大败']:
            result['日柱'].append('十恶大败')
        
        return result

    def calculate_paipan_info(self, sizhu, shishen_info):
        """计算完整的排盘信息"""
        result = {}

        # 获取日柱信息
        day_gan, day_zhi = sizhu['日柱']
        day_pillar = day_gan + day_zhi

        # 导入十神计算器
        from shishen_calculator import ShishenCalculator
        shishen_calc = ShishenCalculator()

        # 计算各柱信息
        for pillar_name, (gan, zhi) in sizhu.items():
            gan_zhi = gan + zhi

            pillar_info = {
                '干支': (gan, zhi),
                '星运': self.calculate_star_fortune(gan, zhi),
                '空亡': self.calculate_kongwang(gan_zhi),  # 每个柱位单独计算空亡
                '纳音': self.calculate_nayin(gan_zhi)
            }

            # 主星：日柱显示主星，其他柱显示天干对应的十神
            if pillar_name == '日柱':
                pillar_info['主星'] = self.calculate_main_star(day_gan)
                pillar_info['自坐'] = self.calculate_self_sitting(day_gan, day_zhi)
            else:
                # 其他柱显示天干对应的十神类型
                pillar_info['主星'] = shishen_calc.determine_shishen(day_gan, gan)
                pillar_info['自坐'] = None

            # 副星：地支藏干对应的十神类型
            pillar_info['副星'] = self._get_deputy_star_from_dizhi(day_gan, zhi, shishen_calc)

            result[pillar_name] = pillar_info

        # 计算神煞
        shensha_result = self.calculate_shensha(sizhu, shishen_info)
        for pillar_name in result:
            result[pillar_name]['神煞'] = shensha_result[pillar_name]

        return result

    def _get_deputy_star_from_dizhi(self, day_gan, zhi, shishen_calc):
        """根据地支藏干计算副星（十神）"""
        # 地支藏干信息
        dizhi_canggan = {
            '子': {'癸': 1.0},
            '丑': {'己': 0.7, '癸': 0.2, '辛': 0.1},
            '寅': {'甲': 0.6, '丙': 0.3, '戊': 0.1},
            '卯': {'乙': 1.0},
            '辰': {'戊': 0.6, '乙': 0.3, '癸': 0.1},
            '巳': {'丙': 0.6, '戊': 0.3, '庚': 0.1},
            '午': {'丁': 0.7, '己': 0.3},
            '未': {'己': 0.6, '丁': 0.3, '乙': 0.1},
            '申': {'庚': 0.6, '壬': 0.3, '戊': 0.1},
            '酉': {'辛': 1.0},
            '戌': {'戊': 0.6, '辛': 0.3, '丁': 0.1},
            '亥': {'壬': 0.7, '甲': 0.3}
        }

        # 获取该地支的藏干
        canggan_dict = dizhi_canggan.get(zhi, {})
        if not canggan_dict:
            return '未知'

        # 找到权重最大的藏干（主气）
        main_canggan = max(canggan_dict.items(), key=lambda x: x[1])[0]

        # 如果主气是日干本身，则取次要藏干
        if main_canggan == day_gan and len(canggan_dict) > 1:
            # 排除日干，找到权重最大的其他藏干
            other_canggan = {k: v for k, v in canggan_dict.items() if k != day_gan}
            if other_canggan:
                main_canggan = max(other_canggan.items(), key=lambda x: x[1])[0]

        # 计算该藏干对应的十神
        if main_canggan != day_gan:
            return shishen_calc.determine_shishen(day_gan, main_canggan)
        else:
            return '比肩'  # 如果只有日干本身，返回比肩
