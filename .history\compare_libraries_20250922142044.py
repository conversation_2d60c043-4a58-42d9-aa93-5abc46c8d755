#!/usr/bin/env python3
"""
对比不同库的排盘结果
测试数据：阳历1999年10月16日16:28分在重庆出生的女
"""

import datetime
from main import BaziSystem

def test_our_system():
    """测试我们的系统"""
    print("=" * 60)
    print("我们的系统排盘结果")
    print("=" * 60)
    
    system = BaziSystem()
    
    user_info = {
        'name': '库对比测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'city_name': '重庆'
    }
    
    try:
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['city_name'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 提取四柱信息
        sizhu = result['基础信息']['四柱']
        print(f"年柱: {sizhu['年柱'][0]}{sizhu['年柱'][1]}")
        print(f"月柱: {sizhu['月柱'][0]}{sizhu['月柱'][1]}")
        print(f"日柱: {sizhu['日柱'][0]}{sizhu['日柱'][1]}")
        print(f"时柱: {sizhu['时柱'][0]}{sizhu['时柱'][1]}")
        
        # 提取阴历信息
        lunar_info = result['基础信息']['阴历信息']
        print(f"阴历: {lunar_info['年']}-{lunar_info['月']}-{lunar_info['日']} {lunar_info['时辰']}")
        
        return sizhu
        
    except Exception as e:
        print(f"我们的系统测试失败: {e}")
        return None

def test_lunar_python():
    """测试lunar_python库"""
    print("\n" + "=" * 60)
    print("lunar_python库排盘结果")
    print("=" * 60)
    
    try:
        from lunar_python import Solar, Lunar
        
        # 创建阳历日期
        solar = Solar.fromYmdHms(1999, 10, 16, 16, 28, 0)
        lunar = solar.getLunar()
        
        print(f"阳历: {solar.toYmd()} {solar.toHms()}")
        print(f"阴历: {lunar.getYearInChinese()}年{lunar.getMonthInChinese()}月{lunar.getDayInChinese()} {lunar.getTimeZhi()}时")
        
        # 获取八字
        eightChar = lunar.getEightChar()
        print(f"年柱: {eightChar.getYear()}")
        print(f"月柱: {eightChar.getMonth()}")
        print(f"日柱: {eightChar.getDay()}")
        print(f"时柱: {eightChar.getTime()}")
        
        return {
            '年柱': (eightChar.getYear()[0], eightChar.getYear()[1]),
            '月柱': (eightChar.getMonth()[0], eightChar.getMonth()[1]),
            '日柱': (eightChar.getDay()[0], eightChar.getDay()[1]),
            '时柱': (eightChar.getTime()[0], eightChar.getTime()[1])
        }
        
    except Exception as e:
        print(f"lunar_python测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_zhdate():
    """测试zhdate库"""
    print("\n" + "=" * 60)
    print("zhdate库排盘结果")
    print("=" * 60)
    
    try:
        from zhdate import ZhDate
        import datetime
        
        # 阳历转阴历
        solar_date = datetime.date(1999, 10, 16)
        zh_date = ZhDate.from_datetime(solar_date)
        
        print(f"阳历: {solar_date}")
        print(f"阴历: {zh_date.chinese()}")
        
        # zhdate主要用于日期转换，不直接提供八字
        print("注意: zhdate库主要用于阳历阴历转换，不直接提供八字排盘功能")
        
        return None
        
    except Exception as e:
        print(f"zhdate测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_lunardate():
    """测试lunardate库"""
    print("\n" + "=" * 60)
    print("lunardate库排盘结果")
    print("=" * 60)
    
    try:
        from lunardate import LunarDate
        import datetime
        
        # 阳历转阴历
        solar_date = datetime.date(1999, 10, 16)
        lunar_date = LunarDate.fromSolarDate(1999, 10, 16)
        
        print(f"阳历: {solar_date}")
        print(f"阴历: {lunar_date}")
        
        # lunardate主要用于日期转换，不直接提供八字
        print("注意: lunardate库主要用于阳历阴历转换，不直接提供八字排盘功能")
        
        return None
        
    except Exception as e:
        print(f"lunardate测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def compare_results(our_result, lunar_python_result):
    """对比结果"""
    print("\n" + "=" * 60)
    print("排盘结果对比")
    print("=" * 60)
    
    if our_result and lunar_python_result:
        print("柱位    我们的系统    lunar_python    是否一致")
        print("-" * 50)
        
        for pillar in ['年柱', '月柱', '日柱', '时柱']:
            our_ganzhi = f"{our_result[pillar][0]}{our_result[pillar][1]}"
            lunar_ganzhi = f"{lunar_python_result[pillar][0]}{lunar_python_result[pillar][1]}"
            is_same = "✓" if our_ganzhi == lunar_ganzhi else "✗"
            print(f"{pillar}    {our_ganzhi:8s}      {lunar_ganzhi:8s}        {is_same}")
    else:
        print("无法进行对比，某个系统返回了空结果")

if __name__ == "__main__":
    print("八字排盘库对比测试")
    print("测试数据：阳历1999年10月16日16:28分在重庆出生的女")
    
    # 测试各个库
    our_result = test_our_system()
    lunar_python_result = test_lunar_python()
    test_zhdate()
    test_lunardate()
    
    # 对比结果
    compare_results(our_result, lunar_python_result)
