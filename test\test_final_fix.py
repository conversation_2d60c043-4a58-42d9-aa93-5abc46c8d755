#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终修复效果
"""

from main import BaziSystem

def test_final_fix():
    """测试最终修复效果"""
    print("=" * 60)
    print("测试最终修复效果")
    print("=" * 60)
    
    system = BaziSystem()
    
    user_info = {
        'name': '最终修复测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 格式化输出
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=False)
        
        # 保存到文件
        with open('最终修复测试_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('✅ 最终修复测试报告已生成：最终修复测试_八字分析_19991016_结果.txt')
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        
        print("\n🎯 最终修复后的流年计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查身强身弱
        shishen_info = result.get('十神分析', {})
        body_analysis = shishen_info.get('身强身弱', {})
        print(f"\n📊 身强身弱：{body_analysis.get('结果', '未知')}")
        
        # 分析修复效果
        liunian_score = liunian_result.get('总得分', 0)
        print(f"\n🔍 修复效果分析：")
        print("-" * 40)
        
        if liunian_score > 0:
            print(f"✅ 修复成功！流年得分已转正：{liunian_score:.2f}分")
            print("符合传统理论：身强格局遇财星流年应该吉利")
        elif liunian_score > -0.5:
            print(f"⚠️ 部分改善：流年得分：{liunian_score:.2f}分（接近平衡）")
            print("基本符合传统理论，但仍有优化空间")
        else:
            print(f"❌ 仍需优化：流年得分：{liunian_score:.2f}分")
            print("与传统理论存在差异，需要进一步调整")
        
        # 对比修复前后
        print(f"\n📈 修复历程回顾：")
        print("-" * 40)
        print("修复前：-1.50分（严重负分）")
        print("第一次优化：-0.30分（权重调整）")
        print(f"最终修复：{liunian_score:.2f}分（地支根气逻辑优化）")
        
        # 传统理论验证
        print(f"\n🎯 传统理论验证：")
        print("-" * 40)
        print("八字：己卯 甲戌 辛丑 丙申")
        print("身强格局：喜克泄耗（官杀、财星、食伤）")
        print("流年甲辰：甲木正财为喜用神")
        print("传统预期：财星流年对身强格局应该是吉利的")
        
        if liunian_score >= 0:
            print("✅ 我们的算法与传统理论一致")
        else:
            print("⚠️ 我们的算法与传统理论存在差异")
        
        print("\n" + "=" * 60)
        print("最终修复测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_fix()
