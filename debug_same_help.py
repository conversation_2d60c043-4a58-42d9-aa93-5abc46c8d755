#!/usr/bin/env python3
"""
调试天干比劫帮扶逻辑
"""

from comprehensive_result import ComprehensiveAnalyzer

def debug_same_help():
    comp = ComprehensiveAnalyzer()
    
    day_gan = '辛'
    gan1 = '甲'
    gan2 = '辛'
    
    print("=== 调试天干比劫帮扶逻辑 ===")
    print(f"日主: {day_gan}")
    print(f"测试: {gan1} vs {gan2}")
    
    # 获取五行信息
    tiangan_info = comp.shishen_calculator.tiangan_info
    day_wuxing = tiangan_info[day_gan]['五行']
    gan1_wuxing = tiangan_info[gan1]['五行']
    gan2_wuxing = tiangan_info[gan2]['五行']
    
    print(f"日主{day_gan}的五行: {day_wuxing}")
    print(f"{gan1}的五行: {gan1_wuxing}")
    print(f"{gan2}的五行: {gan2_wuxing}")
    
    # 检查条件
    print("\n检查条件：")
    print(f"gan1 == day_gan: {gan1} == {day_gan} = {gan1 == day_gan}")
    print(f"gan2_wuxing == day_wuxing: {gan2_wuxing} == {day_wuxing} = {gan2_wuxing == day_wuxing}")
    print(f"gan2 == day_gan: {gan2} == {day_gan} = {gan2 == day_gan}")
    print(f"gan1_wuxing == day_wuxing: {gan1_wuxing} == {day_wuxing} = {gan1_wuxing == day_wuxing}")
    
    # 测试方法
    result = comp._is_same_help(gan1, gan2, day_gan)
    print(f"\n_is_same_help结果: {result}")
    
    # 测试另一种情况：辛 vs 庚（同五行）
    print("\n=== 测试同五行情况 ===")
    gan3 = '庚'
    gan3_wuxing = tiangan_info[gan3]['五行']
    print(f"测试: {day_gan} vs {gan3}")
    print(f"{gan3}的五行: {gan3_wuxing}")
    print(f"gan1 == day_gan: {day_gan} == {day_gan} = {day_gan == day_gan}")
    print(f"gan2_wuxing == day_wuxing: {gan3_wuxing} == {day_wuxing} = {gan3_wuxing == day_wuxing}")
    
    result2 = comp._is_same_help(day_gan, gan3, day_gan)
    print(f"_is_same_help结果: {result2}")

if __name__ == "__main__":
    debug_same_help()
