"""
测试双文件输出功能
"""

from main import BaziSystem

def test_dual_output():
    """测试双文件输出功能"""
    system = BaziSystem()
    
    # 模拟用户信息
    user_info = {
        'name': '测试用户',
        'gender': '男',
        'birth_year': 1991,
        'birth_month': 5,
        'birth_day': 25,
        'birth_hour': 15,
        'birth_minute': 0,
        'city_name': '广州'
    }
    
    # 进行分析
    result = system.analyzer.analyze_complete_bazi(
        birth_year=user_info['birth_year'],
        birth_month=user_info['birth_month'],
        birth_day=user_info['birth_day'],
        birth_hour=user_info['birth_hour'],
        birth_minute=user_info['birth_minute'],
        city_name=user_info['city_name'],
        name=user_info['name'],
        gender=user_info['gender']
    )
    
    # 保存双文件
    system.save_dual_results(user_info, result)
    
    print("双文件输出测试完成！")
    print("请检查生成的两个文件：")
    print("1. 测试用户_八字分析_19910525_结果.txt (简洁版)")
    print("2. 测试用户_八字分析_19910525_详细过程.txt (详细版)")

if __name__ == "__main__":
    test_dual_output()
