#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终验证config参数修复效果的测试
"""

from main import BaziSystem
import json

def test_final_config_fix():
    """最终验证config参数修复效果"""
    print("=" * 60)
    print("最终验证config参数修复效果")
    print("=" * 60)
    
    # 读取当前config
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("📋 当前config.json中您修改的参数：")
    print("-" * 40)
    print(f"地支六冲: {config['地支关系影响系数']['地支六冲']} (您修改为1.5)")
    print(f"天干五合: {config['地支关系影响系数']['天干五合']} (您修改为3)")
    print(f"地支六合: {config['地支关系影响系数']['地支六合']}")
    print(f"相生: {config['地支关系影响系数']['相生']}")
    print(f"相克: {config['地支关系影响系数']['相克']}")
    
    system = BaziSystem()
    
    user_info = {
        'name': '最终config修复验证',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        
        print(f"\n🎯 修复后的流年计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查是否使用了详细计算
        liunian_details = dayun_info.get('流年计算详情', {})
        if liunian_details:
            print(f"\n✅ 使用了详细计算逻辑")
            
            # 检查计算详情中的参数使用
            if '第二步_流年与原局作用关系' in liunian_details:
                step2 = liunian_details['第二步_流年与原局作用关系']
                
                # 检查天干作用详情
                tiangan_zuoyong = step2.get('天干作用详情', [])
                for zuoyong in tiangan_zuoyong:
                    if '天干五合' in zuoyong.get('五行关系', ''):
                        formula = zuoyong.get('计算公式', '')
                        print(f"天干五合计算公式: {formula}")
                        # 检查公式中是否包含正确的系数
                        if str(config['地支关系影响系数']['天干五合']) in formula:
                            print(f"✅ 天干五合系数 {config['地支关系影响系数']['天干五合']} 正确应用")
                        else:
                            print(f"❌ 天干五合系数未正确应用")
                        break
                
                # 检查地支作用详情
                dizhi_zuoyong = step2.get('地支作用详情', [])
                for zuoyong in dizhi_zuoyong:
                    if '地支六冲' in zuoyong.get('五行关系', ''):
                        formula = zuoyong.get('计算公式', '')
                        print(f"地支六冲计算公式: {formula}")
                        if str(config['地支关系影响系数']['地支六冲']) in formula:
                            print(f"✅ 地支六冲系数 {config['地支关系影响系数']['地支六冲']} 正确应用")
                        else:
                            print(f"❌ 地支六冲系数未正确应用")
                        break
        else:
            print(f"❌ 仍在使用简化计算逻辑")
        
        # 保存简化结果
        with open('最终config修复验证_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(f"流年分析结果：\n")
            f.write(f"流年：{liunian_result.get('流年', '未知')}\n")
            f.write(f"流年干支：{liunian_result.get('流年干支', '未知')}\n")
            f.write(f"总得分：{liunian_result.get('总得分', 0):.2f}\n")
            f.write(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}\n")
            f.write(f"运势解读：{liunian_result.get('运势解读', '未知')}\n")

        print('\n✅ 最终config修复验证报告已生成：最终config修复验证_八字分析_19991016_结果.txt')
        
        # 对比预期结果
        print(f"\n📊 修复效果验证：")
        print("-" * 40)
        
        expected_score_change = "应该反映您的config修改"
        actual_score = liunian_result.get('总得分', 0)
        
        print(f"流年总得分: {actual_score:.2f}")
        print(f"期望效果: {expected_score_change}")
        
        if liunian_details:
            print(f"✅ 使用详细计算逻辑 - config参数应该生效")
        else:
            print(f"❌ 仍使用简化逻辑 - config参数未生效")
        
        print("\n" + "=" * 60)
        print("最终config修复验证测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_config_fix()
