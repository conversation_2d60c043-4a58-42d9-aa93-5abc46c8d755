"""
测试天干五合的详细计算
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_wuhe_detail():
    """测试天干五合的详细计算"""
    analyzer = ComprehensiveAnalyzer()
    
    # 模拟一个简单的天干五合计算
    print("=== 测试天干五合详细计算 ===")
    
    # 测试合化成功的情况
    # 假设甲己合化土成功
    # 甲：喜神 +1，己：忌神 -1，化神土：忌神 -1

    final_score, calculation_detail = analyzer._calculate_wuhe_score(
        gan1='甲',
        gan2='己',
        gan1_base_score=1.0,  # 甲为喜神
        gan2_base_score=-1.0,  # 己为忌神
        huashen_wuxing='土',
        is_hehua_success=True,
        xiyong_list=[('比肩', '木')],  # 假设比肩为喜用神
        ji_list=[('正财', '土')],     # 假设正财为忌神，这样化神就是忌神
        day_gan='甲'
    )
    
    print("合化成功计算结果：")
    print(f"最终得分: {final_score}")
    print("计算详情:")
    for key, value in calculation_detail.items():
        print(f"  {key}: {value}")
    
    print("\n" + "="*50)
    
    # 测试合绊的情况
    final_score2, calculation_detail2 = analyzer._calculate_wuhe_score(
        gan1='甲',
        gan2='己', 
        gan1_base_score=1.0,  # 甲为喜神
        gan2_base_score=-1.0,  # 己为忌神
        huashen_wuxing=None,
        is_hehua_success=False,
        xiyong_list=[('比肩', '木')],
        ji_list=[('正财', '土')],
        day_gan='甲'
    )
    
    print("合绊计算结果：")
    print(f"最终得分: {final_score2}")
    print("计算详情:")
    for key, value in calculation_detail2.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_wuhe_detail()
