"""
简单测试五行力量分布显示
"""

try:
    from comprehensive_result import ComprehensiveAnalyzer
    
    print("开始测试...")
    analyzer = ComprehensiveAnalyzer()
    
    # 测试案例
    result = analyzer.analyze_complete_bazi(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=15,
        birth_minute=0,
        city_name="广州",
        name="测试用户",
        gender="男"
    )
    
    print("分析完成，检查数据结构...")
    
    # 检查五行分析数据
    wuxing_info = result['五行分析']
    print(f"五行分析数据键: {list(wuxing_info.keys())}")
    
    if '五行力量' in wuxing_info:
        print("五行力量数据存在:")
        for wuxing, info in wuxing_info['五行力量'].items():
            print(f"  {wuxing}: {info}")
    else:
        print("五行力量数据不存在")
    
    # 生成简洁版输出
    print("\n生成简洁版输出...")
    simple_output = analyzer.format_simple_output(result)
    
    # 检查五行力量分布部分
    lines = simple_output.split('\n')
    in_wuxing_section = False
    print("\n五行力量分布部分:")
    for line in lines:
        if "五行力量分布：" in line:
            in_wuxing_section = True
            print(line)
            continue
        if in_wuxing_section:
            if line.strip() and not line.startswith('【'):
                print(line)
            elif line.startswith('【'):
                break
    
    print("\n测试完成")
    
except Exception as e:
    print(f"测试出错: {e}")
    import traceback
    traceback.print_exc()
