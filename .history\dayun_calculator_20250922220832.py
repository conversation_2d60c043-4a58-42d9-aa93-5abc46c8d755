"""
流年大运计算模块
实现大运起运、流年流月流日分析功能
"""

import json
from datetime import datetime
from lunar_python import Solar


class DayunCalculator:
    def __init__(self, config_path="config.json"):
        """初始化大运计算器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 天干地支序列
        self.tiangan = ['甲', '乙', '丙', '丁', '戊', '己', '庚', '辛', '壬', '癸']
        self.dizhi = ['子', '丑', '寅', '卯', '辰', '巳', '午', '未', '申', '酉', '戌', '亥']
        
        # 天干五行阴阳属性
        self.tiangan_info = {
            '甲': {'五行': '木', '阴阳': '阳'},
            '乙': {'五行': '木', '阴阳': '阴'},
            '丙': {'五行': '火', '阴阳': '阳'},
            '丁': {'五行': '火', '阴阳': '阴'},
            '戊': {'五行': '土', '阴阳': '阳'},
            '己': {'五行': '土', '阴阳': '阴'},
            '庚': {'五行': '金', '阴阳': '阳'},
            '辛': {'五行': '金', '阴阳': '阴'},
            '壬': {'五行': '水', '阴阳': '阳'},
            '癸': {'五行': '水', '阴阳': '阴'}
        }
        
        # 地支关系
        self.dizhi_relations = {
            '六冲': [('子', '午'), ('丑', '未'), ('寅', '申'), ('卯', '酉'), ('辰', '戌'), ('巳', '亥')],
            '六合': [('子', '丑'), ('寅', '亥'), ('卯', '戌'), ('辰', '酉'), ('巳', '申'), ('午', '未')],
            '三合': [('申', '子', '辰'), ('亥', '卯', '未'), ('寅', '午', '戌'), ('巳', '酉', '丑')],
            '相刑': [('寅', '巳', '申'), ('丑', '未', '戌'), ('子', '卯')],
            '相害': [('子', '未'), ('丑', '午'), ('寅', '巳'), ('卯', '辰'), ('申', '亥'), ('酉', '戌')]
        }
        
        # 天干五合
        self.tiangan_wuhe = [('甲', '己'), ('乙', '庚'), ('丙', '辛'), ('丁', '壬'), ('戊', '癸')]
    
    def calculate_qiyun_time(self, birth_info, gender):
        """计算起运时间"""
        year_gan = birth_info['四柱']['年柱'][0]
        birth_date = datetime.strptime(birth_info['真太阳时'], "%Y年%m月%d日 %H时%M分")

        # 判断阳干阴干
        yang_gan = ['甲', '丙', '戊', '庚', '壬']
        is_yang_gan = year_gan in yang_gan

        # 确定顺排还是逆排
        if (is_yang_gan and gender == '男') or (not is_yang_gan and gender == '女'):
            # 阳男阴女：顺排
            direction = '顺排'
            jieqi_days = self._find_next_jie(birth_date)
        else:
            # 阴男阳女：逆排
            direction = '逆排'
            jieqi_days = self._find_prev_jie(birth_date)

        # 计算起运岁数和月份 (3天=1年，1天=4个月)
        qiyun_years = int(jieqi_days // 3)
        remaining_days = jieqi_days % 3
        qiyun_months = int(remaining_days * 4)

        return {
            '起运方向': direction,
            '起运岁数': qiyun_years,
            '起运月份': qiyun_months,
            '距离节气天数': round(jieqi_days, 2)
        }

    def _find_next_jie(self, birth_date):
        """找出生后第一个节气的天数差"""
        # 12个节气（不包括气）
        jie_names = ['立春', '惊蛰', '清明', '立夏', '芒种', '小暑',
                     '立秋', '白露', '寒露', '立冬', '大雪', '小寒']

        # 使用lunar_python获取节气列表
        solar = Solar.fromYmdHms(birth_date.year, birth_date.month, birth_date.day,
                               birth_date.hour, birth_date.minute, birth_date.second)
        lunar = solar.getLunar()

        # 使用简化的节气计算方法
        # 由于lunar_python的API复杂，我们使用基于月份的近似计算
        birth_month = birth_date.month
        birth_day = birth_date.day

        # 每个月的节气大致日期（简化处理）
        jie_dates = {
            1: 5,   # 小寒
            2: 4,   # 立春
            3: 6,   # 惊蛰
            4: 5,   # 清明
            5: 6,   # 立夏
            6: 6,   # 芒种
            7: 7,   # 小暑
            8: 8,   # 立秋
            9: 8,   # 白露
            10: 8,  # 寒露
            11: 7,  # 立冬
            12: 7   # 大雪
        }

        # 计算到下个节气的天数
        if birth_day < jie_dates[birth_month]:
            # 本月节气还没到
            days_to_jie = jie_dates[birth_month] - birth_day
        else:
            # 本月节气已过，计算到下个月节气的天数
            next_month = birth_month + 1 if birth_month < 12 else 1
            days_in_current_month = 31 if birth_month in [1,3,5,7,8,10,12] else (30 if birth_month in [4,6,9,11] else 28)
            days_to_month_end = days_in_current_month - birth_day
            days_to_jie = days_to_month_end + jie_dates[next_month]

        return float(days_to_jie)

        # 如果当年没找到，查找下一年的第一个节气
        next_year_solar = Solar.fromYmdHms(birth_date.year + 1, 1, 1, 0, 0, 0)
        next_year_lunar = next_year_solar.getLunar()
        next_year_jieqi = next_year_lunar.getJieQiList()

        for jieqi_name, jieqi_solar in next_year_jieqi.items():
            if jieqi_name in jie_names:
                jieqi_datetime = datetime(jieqi_solar.getYear(), jieqi_solar.getMonth(),
                                        jieqi_solar.getDay(), jieqi_solar.getHour(),
                                        jieqi_solar.getMinute(), jieqi_solar.getSecond())
                time_diff = jieqi_datetime - birth_date
                return time_diff.total_seconds() / (24 * 3600)  # 转换为天数

        # 如果还没找到，返回默认值
        return 15.0

    def _find_prev_jie(self, birth_date):
        """找出生前最后一个节气的天数差"""
        # 使用简化的节气计算方法
        birth_month = birth_date.month
        birth_day = birth_date.day

        # 每个月的节气大致日期（简化处理）
        jie_dates = {
            1: 5,   # 小寒
            2: 4,   # 立春
            3: 6,   # 惊蛰
            4: 5,   # 清明
            5: 6,   # 立夏
            6: 6,   # 芒种
            7: 7,   # 小暑
            8: 8,   # 立秋
            9: 8,   # 白露
            10: 8,  # 寒露
            11: 7,  # 立冬
            12: 7   # 大雪
        }

        # 计算到上个节气的天数
        if birth_day > jie_dates[birth_month]:
            # 本月节气已过
            days_from_jie = birth_day - jie_dates[birth_month]
        else:
            # 本月节气还没到，计算到上个月节气的天数
            prev_month = birth_month - 1 if birth_month > 1 else 12
            days_in_prev_month = 31 if prev_month in [1,3,5,7,8,10,12] else (30 if prev_month in [4,6,9,11] else 28)
            days_from_prev_month_jie = days_in_prev_month - jie_dates[prev_month]
            days_from_jie = days_from_prev_month_jie + birth_day

        return float(days_from_jie)
    
    def generate_dayun_sequence(self, month_pillar, direction, steps=8):
        """生成大运序列"""
        month_gan, month_zhi = month_pillar[0], month_pillar[1]
        
        gan_index = self.tiangan.index(month_gan)
        zhi_index = self.dizhi.index(month_zhi)
        
        dayun_list = []
        
        for i in range(steps):
            if direction == '顺排':
                new_gan_index = (gan_index + i + 1) % 10
                new_zhi_index = (zhi_index + i + 1) % 12
            else:  # 逆排
                new_gan_index = (gan_index - i - 1) % 10
                new_zhi_index = (zhi_index - i - 1) % 12
            
            dayun_gan = self.tiangan[new_gan_index]
            dayun_zhi = self.dizhi[new_zhi_index]
            
            dayun_list.append(dayun_gan + dayun_zhi)
        
        return dayun_list
    
    def calculate_dayun_ages(self, qiyun_info, steps=8):
        """计算各步大运的年龄范围"""
        qiyun_age = qiyun_info['起运岁数']
        
        dayun_ages = []
        for i in range(steps):
            start_age = qiyun_age + i * 10
            end_age = start_age + 9
            dayun_ages.append((start_age, end_age))
        
        return dayun_ages
    
    def get_relation_type(self, char1, char2):
        """获取两个字符的关系类型"""
        # 检查地支关系
        for relation_name, relations in self.dizhi_relations.items():
            for relation in relations:
                if len(relation) == 2:  # 六冲、六合、相害
                    if (char1, char2) in [relation, relation[::-1]]:
                        return relation_name, self.config['地支关系影响系数'][relation_name]
                elif len(relation) == 3:  # 三合、相刑
                    if char1 in relation and char2 in relation:
                        return relation_name, self.config['地支关系影响系数'][relation_name]
        
        # 检查天干五合
        for he_pair in self.tiangan_wuhe:
            if (char1, char2) in [he_pair, he_pair[::-1]]:
                return '天干五合', self.config['地支关系影响系数']['天干五合']
        
        return '普通生克', self.config['地支关系影响系数']['普通生克']
    
    def analyze_liunian(self, birth_info, dayun, xiyong_info, target_date):
        """分析流年"""
        from shishen_calculator import ShishenCalculator

        target_year = target_date['年']

        # 获取流年干支
        solar = Solar.fromYmd(target_year, 1, 1)
        lunar = solar.getLunar()
        liunian_ganzhi = lunar.getYearInGanZhi()

        liunian_gan, liunian_zhi = liunian_ganzhi[0], liunian_ganzhi[1]

        # 初始化十神计算器
        shishen_calc = ShishenCalculator()

        # 获取日干
        day_gan = birth_info['四柱']['日柱'][0]

        # 判断流年干支的喜忌
        xiyong_list, ji_list = xiyong_info

        def get_xiji_score(shishen, xiyong_list, ji_list):
            """根据十神判断喜忌得分"""
            # 检查是否为喜用神
            for xi_shishen, weight in xiyong_list:
                if shishen == xi_shishen:
                    return 1.0, "喜用神"

            # 检查是否为忌神
            for ji_shishen, weight in ji_list:
                if shishen == ji_shishen:
                    return -1.0, "忌神"

            return 0.0, "中性"

        # 计算流年天干的十神和得分
        liunian_gan_shishen = shishen_calc.determine_shishen(day_gan, liunian_gan)
        liunian_gan_score, liunian_gan_type = get_xiji_score(liunian_gan_shishen, xiyong_list, ji_list)

        # 计算流年地支的十神和得分（通过本气）
        from wuxing_calculator import WuxingCalculator
        wuxing_calc = WuxingCalculator()
        liunian_zhi_bengqi = list(wuxing_calc.dizhi_info[liunian_zhi]['藏干'].keys())[0]
        liunian_zhi_shishen = shishen_calc.determine_shishen(day_gan, liunian_zhi_bengqi)
        liunian_zhi_score, liunian_zhi_type = get_xiji_score(liunian_zhi_shishen, xiyong_list, ji_list)

        # 计算与原局的作用关系
        total_score = 0

        # 流年天干与原局天干作用
        sizhu = birth_info['四柱']
        for pos, gan in [('年干', sizhu['年柱'][0]), ('月干', sizhu['月柱'][0]),
                        ('日干', sizhu['日柱'][0]), ('时干', sizhu['时柱'][0])]:
            if gan != liunian_gan:  # 避免自己与自己作用
                relation_type, coefficient = self.get_relation_type(liunian_gan, gan)
                gan_shishen = shishen_calc.determine_shishen(day_gan, gan)
                gan_base_score, gan_type = get_xiji_score(gan_shishen, xiyong_list, ji_list)
                contribution = gan_base_score * coefficient
                total_score += contribution

        # 流年地支与原局地支作用
        for pos, zhi in [('年支', sizhu['年柱'][1]), ('月支', sizhu['月柱'][1]),
                        ('日支', sizhu['日柱'][1]), ('时支', sizhu['时柱'][1])]:
            if zhi != liunian_zhi:  # 避免自己与自己作用
                relation_type, coefficient = self.get_relation_type(liunian_zhi, zhi)
                zhi_bengqi = list(wuxing_calc.dizhi_info[zhi]['藏干'].keys())[0]
                zhi_shishen = shishen_calc.determine_shishen(day_gan, zhi_bengqi)
                zhi_base_score, zhi_type = get_xiji_score(zhi_shishen, xiyong_list, ji_list)
                contribution = zhi_base_score * coefficient
                total_score += contribution

        # 加上流年自身得分
        total_score += liunian_gan_score + liunian_zhi_score

        # 判断吉凶等级
        if total_score >= self.config['吉凶等级判定']['大吉']:
            level = '大吉'
            desc = '诸事顺遂'
        elif total_score >= self.config['吉凶等级判定']['吉']:
            level = '吉'
            desc = '小有收获'
        elif total_score > self.config['吉凶等级判定']['凶']:
            level = '平'
            desc = '平淡无奇'
        elif total_score >= self.config['吉凶等级判定']['大凶']:
            level = '凶'
            desc = '阻力频生'
        else:
            level = '大凶'
            desc = '动荡不安'

        return {
            '流年': f"{target_year}年",
            '流年干支': liunian_ganzhi,
            '总得分': round(total_score, 2),
            '吉凶等级': level,
            '运势解读': desc
        }
    
    def analyze_liuyue(self, liunian_result, target_date):
        """分析流月"""
        target_month = target_date['月']

        # 获取流月干支
        solar = Solar.fromYmd(target_date['年'], target_month, 1)
        lunar = solar.getLunar()
        liuyue_ganzhi = lunar.getMonthInGanZhi()

        # 简化计算流月得分 - 基于流年得分的30%
        base_score = liunian_result['总得分'] * 0.3

        return {
            '流月': f"{target_month}月",
            '流月干支': liuyue_ganzhi,
            '总得分': round(base_score, 2),
            '基于流年': liunian_result['流年']
        }
    
    def analyze_liuri(self, liunian_result, liuyue_result, target_date):
        """分析流日"""
        # 获取流日干支
        solar = Solar.fromYmd(target_date['年'], target_date['月'], target_date['日'])
        lunar = solar.getLunar()
        liuri_ganzhi = lunar.getDayInGanZhi()

        # 简化计算流日得分 - 基于流年和流月得分的10%
        base_score = (liunian_result['总得分'] + liuyue_result['总得分']) * 0.1

        return {
            '流日': f"{target_date['月']}月{target_date['日']}日",
            '流日干支': liuri_ganzhi,
            '总得分': round(base_score, 2),
            '基于流年流月': f"{liunian_result['流年']}{liuyue_result['流月']}"
        }
    
    def format_dayun_output(self, qiyun_info, dayun_list, dayun_ages, 
                           liunian_result, liuyue_result, liuri_result):
        """格式化大运分析输出"""
        output = f"""
=== 流年大运分析结果 ===

起运信息：
起运方向：{qiyun_info['起运方向']}
起运岁数：{qiyun_info['起运岁数']}岁{qiyun_info['起运月份']}个月

大运序列：
"""
        for i, (dayun, (start_age, end_age)) in enumerate(zip(dayun_list, dayun_ages)):
            output += f"{start_age}-{end_age}岁：{dayun}运\n"
        
        output += f"""
流年分析：
{liunian_result['流年']} ({liunian_result['流年干支']})
总得分：{liunian_result['总得分']}
吉凶等级：{liunian_result['吉凶等级']}
运势解读：{liunian_result['运势解读']}

流月分析：
{liuyue_result['流月']} ({liuyue_result['流月干支']})
总得分：{liuyue_result['总得分']}

流日分析：
{liuri_result['流日']} ({liuri_result['流日干支']})
总得分：{liuri_result['总得分']}
"""
        
        return output


# 测试函数
def test_dayun_calculator():
    """测试大运计算模块"""
    from calendar_converter import CalendarConverter
    from shishen_calculator import ShishenCalculator
    
    # 获取基础信息
    converter = CalendarConverter()
    birth_result = converter.process_birth_info(
        birth_year=1990, birth_month=5, birth_day=15,
        birth_hour=14, birth_minute=30, city_name="广州", name="测试用户"
    )
    
    shishen_calc = ShishenCalculator()
    shishen_weights = shishen_calc.calculate_shishen_weights(birth_result['四柱'])
    body_analysis = shishen_calc.analyze_body_strength(shishen_weights)
    xiyong_info = shishen_calc.determine_xiyongshen(body_analysis[0], shishen_weights)
    
    # 大运分析
    calculator = DayunCalculator()
    qiyun_info = calculator.calculate_qiyun_time(birth_result, '男')
    dayun_list = calculator.generate_dayun_sequence(
        birth_result['四柱']['月柱'], qiyun_info['起运方向']
    )
    dayun_ages = calculator.calculate_dayun_ages(qiyun_info)
    
    # 流年流月流日分析
    target_date = calculator.config['流年流月流日分析日期']
    liunian_result = calculator.analyze_liunian(birth_result, dayun_list[0], xiyong_info, target_date)
    liuyue_result = calculator.analyze_liuyue(liunian_result, target_date)
    liuri_result = calculator.analyze_liuri(liunian_result, liuyue_result, target_date)
    
    print(calculator.format_dayun_output(
        qiyun_info, dayun_list, dayun_ages,
        liunian_result, liuyue_result, liuri_result
    ))
    
    return qiyun_info, dayun_list


if __name__ == "__main__":
    test_dayun_calculator()
