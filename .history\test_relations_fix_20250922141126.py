#!/usr/bin/env python3
"""
测试修复后的关系计算
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_relations():
    comp = ComprehensiveAnalyzer()
    
    # 测试数据
    sizhu = {
        '年柱': ('己', '卯'),
        '月柱': ('甲', '戌'),
        '日柱': ('辛', '丑'),
        '时柱': ('丙', '申')
    }
    
    birth_info = {
        '四柱': sizhu
    }
    
    print("=== 测试修复后的关系计算 ===")
    
    day_gan = '辛'
    
    # 测试各种天干关系
    test_cases = [
        ('甲', '辛', '甲vs辛(日主)'),  # 应该是天干比劫帮扶
        ('己', '辛', '己vs辛(日主)'),  # 应该是印星生扶  
        ('甲', '己', '甲vs己'),       # 普通关系
        ('丙', '辛', '丙vs辛(日主)'), # 普通关系
    ]
    
    print("天干关系测试：")
    for gan1, gan2, desc in test_cases:
        result = comp._calculate_tiangan_relation(gan1, gan2, birth_info)
        if len(result) == 3:
            relation, coefficient, is_success = result
            print(f"  {desc}: {relation} (权重: {coefficient}, 成功: {is_success})")
        else:
            relation, coefficient = result
            print(f"  {desc}: {relation} (权重: {coefficient})")
    
    # 测试地支关系
    print("\n地支关系测试：")
    test_zhi_cases = [
        ('辰', '卯', '辰vs卯'),
        ('辰', '戌', '辰vs戌'),
        ('辰', '丑', '辰vs丑'),
        ('辰', '申', '辰vs申'),
    ]
    
    for zhi1, zhi2, desc in test_zhi_cases:
        relation, coefficient = comp._calculate_dizhi_relation(zhi1, zhi2, birth_info)
        print(f"  {desc}: {relation} (权重: {coefficient})")
    
    print("\n=== 检查配置权重 ===")
    print("配置文件中的权重：")
    for key, value in comp.config['地支关系影响系数'].items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    test_relations()
