#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试流年地支计算明细修复
"""

from main import BaziSystem

def test_liunian_fix():
    """测试流年地支计算明细修复"""
    print("=" * 60)
    print("测试流年地支计算明细修复")
    print("=" * 60)
    
    system = BaziSystem()
    
    user_info = {
        'name': '流年修复测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 格式化详细输出
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=True)
        
        # 保存到文件
        with open('流年修复测试_八字分析_19991016_详细过程.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('✅ 流年修复测试报告已生成：流年修复测试_八字分析_19991016_详细过程.txt')
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        
        print("\n🎯 流年计算结果验证：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')}")
        print(f"干支：{liunian_result.get('流年干支', '未知')}")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查是否有地支作用详情
        liunian_details = dayun_info.get('计算详情', {})
        if '第四步_流年与原局作用关系' in liunian_details:
            dizhi_zuoyong = liunian_details['第四步_流年与原局作用关系'].get('地支作用详情', [])
            print(f"\n📊 地支作用详情数量：{len(dizhi_zuoyong)}个")
            for i, zuoyong in enumerate(dizhi_zuoyong[:3]):  # 显示前3个
                print(f"  {i+1}. {zuoyong.get('原局', '未知')} - {zuoyong.get('五行关系', '未知')}")
        
        print("\n" + "=" * 60)
        print("流年修复测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_liunian_fix()
