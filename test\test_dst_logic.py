"""
测试夏令时逻辑
"""

from datetime import datetime, timed<PERSON><PERSON>

def get_dst_start_date(year):
    """获取指定年份夏令时开始日期：4月中旬第一个星期日2时"""
    # 4月15日作为中旬基准
    mid_april = datetime(year, 4, 15)
    
    # 找到4月15日当周或之后的第一个星期日
    days_until_sunday = (6 - mid_april.weekday()) % 7
    if days_until_sunday == 0 and mid_april.weekday() != 6:
        days_until_sunday = 7
    
    dst_start = mid_april + timedelta(days=days_until_sunday)
    # 设置为2时
    dst_start = dst_start.replace(hour=2, minute=0, second=0, microsecond=0)
    
    return dst_start

def get_dst_end_date(year):
    """获取指定年份夏令时结束日期：9月中旬第一个星期日2时"""
    # 9月15日作为中旬基准
    mid_september = datetime(year, 9, 15)
    
    # 找到9月15日当周或之后的第一个星期日
    days_until_sunday = (6 - mid_september.weekday()) % 7
    if days_until_sunday == 0 and mid_september.weekday() != 6:
        days_until_sunday = 7
    
    dst_end = mid_september + timedelta(days=days_until_sunday)
    # 设置为2时
    dst_end = dst_end.replace(hour=2, minute=0, second=0, microsecond=0)
    
    return dst_end

def test_dst_dates():
    """测试夏令时日期计算"""
    print("=== 夏令时日期计算测试 ===")
    
    for year in [1986, 1987, 1988, 1989, 1990, 1991]:
        dst_start = get_dst_start_date(year)
        dst_end = get_dst_end_date(year)
        
        print(f"{year}年:")
        print(f"  夏令时开始：{dst_start.strftime('%Y年%m月%d日 %H时')} (星期{dst_start.weekday()+1})")
        print(f"  夏令时结束：{dst_end.strftime('%Y年%m月%d日 %H时')} (星期{dst_end.weekday()+1})")
    
    # 测试具体日期是否在夏令时期间
    print("\n=== 具体日期测试 ===")
    test_cases = [
        (1989, 7, 15, 14, 30),  # 夏令时期间
        (1989, 2, 15, 14, 30),  # 非夏令时期间
        (1995, 7, 15, 14, 30),  # 不在实施期间
    ]
    
    for year, month, day, hour, minute in test_cases:
        test_time = datetime(year, month, day, hour, minute)
        
        if year < 1986 or year > 1991:
            print(f"{test_time.strftime('%Y年%m月%d日 %H时%M分')}: 不在夏令时实施期间")
            continue
        
        dst_start = get_dst_start_date(year)
        dst_end = get_dst_end_date(year)
        
        if dst_start <= test_time < dst_end:
            beijing_time = test_time - timedelta(hours=1)
            print(f"{test_time.strftime('%Y年%m月%d日 %H时%M分')}: 夏令时期间")
            print(f"  转换后北京时间: {beijing_time.strftime('%Y年%m月%d日 %H时%M分')}")
        else:
            print(f"{test_time.strftime('%Y年%m月%d日 %H时%M分')}: 非夏令时期间")
            print(f"  北京时间: {test_time.strftime('%Y年%m月%d日 %H时%M分')}")

if __name__ == "__main__":
    test_dst_dates()
