#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试流年计算方法
"""

from main import BaziSystem
import json

def debug_liunian_calculation():
    """调试流年计算方法"""
    print("=" * 60)
    print("调试流年计算方法")
    print("=" * 60)
    
    system = BaziSystem()
    
    user_info = {
        'name': '调试测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用完整分析获取所需信息
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )

        calendar_result = result['基本信息']
        xiyong_info = result['十神分析']['喜用神忌神']
        
        # 直接调用流年详细计算方法
        target_date = system.analyzer.config['流年流月流日分析日期']
        
        print(f"📋 调试信息：")
        print(f"目标日期: {target_date}")
        print(f"四柱: {calendar_result['四柱']}")
        print(f"喜用神: {xiyong_info[0]}")
        print(f"忌神: {xiyong_info[1]}")
        
        # 调用详细计算方法
        liunian_details = system.analyzer._calculate_liunian_detailed_steps(
            None, xiyong_info, target_date, calendar_result
        )
        
        print(f"\n🔍 详细计算结果：")
        print("-" * 40)
        
        for step_name, step_data in liunian_details.items():
            print(f"\n{step_name}:")
            if isinstance(step_data, dict):
                for key, value in step_data.items():
                    if key == "总得分":
                        print(f"  ✅ {key}: {value}")
                    else:
                        print(f"  {key}: {value}")
            else:
                print(f"  {step_data}")
        
        # 检查第五步的总得分
        if '第五步_综合得分计算' in liunian_details:
            step5 = liunian_details['第五步_综合得分计算']
            total_score = step5.get('总得分', 'NOT_FOUND')
            print(f"\n🎯 关键结果：")
            print(f"第五步总得分: {total_score}")
            
            if total_score != 'NOT_FOUND' and total_score != '0.00':
                print(f"✅ 计算成功，得分为: {total_score}")
            else:
                print(f"❌ 计算失败或得分为0")
                
                # 检查天干和地支得分
                print(f"\n详细分析：")
                print(f"天干小计: {step5.get('天干小计', 'NOT_FOUND')}")
                print(f"地支小计: {step5.get('地支小计', 'NOT_FOUND')}")
                print(f"喜用神加分: {step5.get('喜用神加分', 'NOT_FOUND')}")
        else:
            print(f"\n❌ 未找到第五步_综合得分计算")
        
        # 检查第四步的作用关系
        if '第四步_流年与原局作用关系' in liunian_details:
            step4 = liunian_details['第四步_流年与原局作用关系']
            print(f"\n🔍 第四步作用关系：")
            
            tiangan_zuoyong = step4.get('天干作用详情', [])
            print(f"天干作用数量: {len(tiangan_zuoyong)}")
            for i, zuoyong in enumerate(tiangan_zuoyong):
                print(f"  天干{i+1}: {zuoyong.get('五行关系', 'UNKNOWN')} -> 得分: {zuoyong.get('得分', 'UNKNOWN')}")
            
            dizhi_zuoyong = step4.get('地支作用详情', [])
            print(f"地支作用数量: {len(dizhi_zuoyong)}")
            for i, zuoyong in enumerate(dizhi_zuoyong):
                print(f"  地支{i+1}: {zuoyong.get('五行关系', 'UNKNOWN')} -> 得分: {zuoyong.get('得分', 'UNKNOWN')}")
        
        print("\n" + "=" * 60)
        print("调试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_liunian_calculation()
