#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试config参数修复效果
"""

from main import BaziSystem
import json

def test_config_parameters():
    """测试config参数是否正确生效"""
    print("=" * 60)
    print("测试config参数修复效果")
    print("=" * 60)
    
    # 读取当前config
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("📋 当前config.json参数：")
    print("-" * 40)
    print(f"克制系数: {config['五行计算参数']['克制系数']}")
    print(f"地支六冲: {config['地支关系影响系数']['地支六冲']}")
    print(f"地支相刑: {config['地支关系影响系数']['地支相刑']}")
    print(f"地支相害: {config['地支关系影响系数']['地支相害']}")
    print(f"本气权重: {config['藏干权重']['本气']}")
    print(f"本气_中气权重: {config['藏干权重']['本气_中气']}")
    print(f"本气_中气_余气权重: {config['藏干权重']['本气_中气_余气']}")
    
    system = BaziSystem()
    
    user_info = {
        'name': 'config参数测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 格式化输出
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=False)
        
        # 保存到文件
        with open('config参数测试_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('\n✅ config参数测试报告已生成：config参数测试_八字分析_19991016_结果.txt')
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        
        print("\n🎯 config参数生效后的流年计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查藏干权重是否生效
        print("\n🔍 藏干权重检查：")
        print("-" * 40)
        
        # 检查shishen_calculator的藏干权重
        shishen_calc = system.analyzer.shishen_calculator
        print("shishen_calculator中的藏干权重：")
        for zhi in ['丑', '寅', '午']:
            canggan_dict = shishen_calc.dizhi_canggan[zhi]
            print(f"  {zhi}: {canggan_dict}")
        
        # 检查wuxing_calculator的藏干权重
        wuxing_calc = system.analyzer.wuxing_calculator
        print("\nwuxing_calculator中的藏干权重：")
        for zhi in ['丑', '寅', '午']:
            canggan_dict = wuxing_calc.dizhi_info[zhi]['藏干']
            print(f"  {zhi}: {canggan_dict}")
        
        # 验证参数是否与config一致
        print("\n✅ 参数一致性验证：")
        print("-" * 40)
        
        # 检查丑的藏干权重
        chou_canggan = shishen_calc.dizhi_canggan['丑']
        expected_chou = {
            '己': config['藏干权重']['本气_中气_余气']['本气'],
            '癸': config['藏干权重']['本气_中气_余气']['中气'],
            '辛': config['藏干权重']['本气_中气_余气']['余气']
        }
        
        if chou_canggan == expected_chou:
            print("✅ 藏干权重配置正确生效")
        else:
            print("❌ 藏干权重配置未生效")
            print(f"实际：{chou_canggan}")
            print(f"期望：{expected_chou}")
        
        # 检查五行计算参数
        wuxing_config = wuxing_calc.config['五行计算参数']
        if wuxing_config['克制系数'] == config['五行计算参数']['克制系数']:
            print("✅ 五行计算参数正确生效")
        else:
            print("❌ 五行计算参数未生效")
        
        # 检查地支关系影响系数
        comprehensive_config = system.analyzer.config['地支关系影响系数']
        if comprehensive_config['地支六冲'] == config['地支关系影响系数']['地支六冲']:
            print("✅ 地支关系影响系数正确生效")
        else:
            print("❌ 地支关系影响系数未生效")
        
        print("\n" + "=" * 60)
        print("config参数修复测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_parameters()
