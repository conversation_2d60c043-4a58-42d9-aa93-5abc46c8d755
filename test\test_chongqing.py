"""
测试重庆经度的真太阳时计算
"""

from comprehensive_result import ComprehensiveAnalyzer

def test_chongqing_time():
    """测试重庆的真太阳时计算"""
    analyzer = ComprehensiveAnalyzer()
    
    # 测试重庆的案例
    result = analyzer.analyze_complete_bazi(
        birth_year=1999,
        birth_month=10,
        birth_day=16,
        birth_hour=16,
        birth_minute=28,
        city_name="重庆",
        name="赖嘉瑶",
        gender="女"
    )
    
    print("=== 重庆真太阳时计算测试 ===")
    basic_info = result['基本信息']
    
    print(f"出生地：{basic_info['出生地']}")
    print(f"经度：{basic_info['经度']}°")
    print(f"北京时间：{basic_info['北京时间']}")
    print(f"经度时差：{basic_info['经度时差']}")
    print(f"均时差：{basic_info['均时差']}")
    print(f"真太阳时：{basic_info['真太阳时']}")
    
    # 手动验证计算
    longitude = 106.5516
    longitude_diff = (longitude - 120) * 4
    print(f"\n手动验证：")
    print(f"经度时差计算：({longitude} - 120) × 4 = {longitude_diff:.2f}分钟")
    print(f"重庆在北京以西，时间应该比北京时间慢")
    
    # 保存详细结果
    detailed_result = analyzer.format_comprehensive_output(result, show_details=True)
    with open("重庆真太阳时测试_详细过程.txt", "w", encoding="utf-8") as f:
        f.write(detailed_result)
    
    print("详细结果已保存到: 重庆真太阳时测试_详细过程.txt")

if __name__ == "__main__":
    test_chongqing_time()
