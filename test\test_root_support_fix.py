#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试地支根气修复效果
"""

from main import BaziSystem

def test_root_support_fix():
    """测试地支根气修复效果"""
    print("=" * 60)
    print("测试地支根气修复效果")
    print("=" * 60)
    
    system = BaziSystem()
    
    user_info = {
        'name': '地支根气修复测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 格式化输出
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=False)
        
        # 保存到文件
        with open('地支根气修复测试_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('✅ 地支根气修复测试报告已生成：地支根气修复测试_八字分析_19991016_结果.txt')
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        
        print("\n🎯 修复后的流年计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 分析地支根气的影响
        print("\n📊 地支根气分析：")
        print("-" * 40)
        print("辛金的根气地支：酉(禄)、申(刃)、子(长生)、辰(墓)")
        print("流年甲辰：")
        print("  - 甲(木)：正财，喜用神")
        print("  - 辰(土)：是辛金的墓库，应该有地支根气关系")
        
        # 检查修复效果
        liunian_score = liunian_result.get('总得分', 0)
        if liunian_score > 0:
            print(f"\n✅ 修复成功！流年得分已转正：{liunian_score:.2f}分")
        elif liunian_score > -0.5:
            print(f"\n⚠️ 部分改善：流年得分：{liunian_score:.2f}分（接近平衡）")
        else:
            print(f"\n❌ 仍需优化：流年得分：{liunian_score:.2f}分")
        
        # 对比传统理论
        print("\n🔍 传统理论对比：")
        print("-" * 40)
        print("身强格局 + 财星流年 = 理论上应该吉利")
        print("甲木正财为喜用神，辰土为日主墓库")
        print("传统算法：财星流年对身强格局通常是好的")
        
        print("\n" + "=" * 60)
        print("地支根气修复测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_root_support_fix()
