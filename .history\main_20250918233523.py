"""
八字占卜系统主程序
提供命令行交互界面和API接口
"""

import json
import sys
from comprehensive_result import ComprehensiveAnalyzer


class BaziSystem:
    def __init__(self):
        """初始化八字系统"""
        self.analyzer = ComprehensiveAnalyzer()
        self.load_city_list()
    
    def load_city_list(self):
        """加载城市列表"""
        try:
            with open('city_coordinates.json', 'r', encoding='utf-8') as f:
                city_data = json.load(f)
                self.city_list = list(city_data["中国主要城市经度"].keys())
        except Exception as e:
            print(f"加载城市数据失败: {e}")
            self.city_list = ["北京", "上海", "广州", "深圳"]
    
    def display_welcome(self):
        """显示欢迎信息"""
        print("="*60)
        print("           欢迎使用八字占卜系统")
        print("="*60)
        print("本系统可以为您提供：")
        print("1. 阳历阴历换算")
        print("2. 五行力量分析")
        print("3. 十神分析")
        print("4. 大运流年分析")
        print("5. 综合命理解读")
        print("="*60)
    
    def get_user_input(self):
        """获取用户输入信息"""
        print("\n请输入您的出生信息：")
        
        try:
            name = input("姓名：").strip()
            if not name:
                name = "用户"
            
            # 性别
            while True:
                gender = input("性别 (男/女)：").strip()
                if gender in ['男', '女']:
                    break
                print("请输入'男'或'女'")
            
            # 出生年月日
            while True:
                try:
                    birth_year = int(input("出生年份 (如1990)："))
                    if 1900 <= birth_year <= 2100:
                        break
                    print("请输入1900-2100之间的年份")
                except ValueError:
                    print("请输入有效的年份数字")
            
            while True:
                try:
                    birth_month = int(input("出生月份 (1-12)："))
                    if 1 <= birth_month <= 12:
                        break
                    print("请输入1-12之间的月份")
                except ValueError:
                    print("请输入有效的月份数字")
            
            while True:
                try:
                    birth_day = int(input("出生日期 (1-31)："))
                    if 1 <= birth_day <= 31:
                        break
                    print("请输入1-31之间的日期")
                except ValueError:
                    print("请输入有效的日期数字")
            
            # 出生时间
            while True:
                try:
                    birth_hour = int(input("出生小时 (0-23)："))
                    if 0 <= birth_hour <= 23:
                        break
                    print("请输入0-23之间的小时")
                except ValueError:
                    print("请输入有效的小时数字")
            
            while True:
                try:
                    birth_minute = int(input("出生分钟 (0-59)："))
                    if 0 <= birth_minute <= 59:
                        break
                    print("请输入0-59之间的分钟")
                except ValueError:
                    print("请输入有效的分钟数字")
            
            # 出生地点
            print(f"\n可选城市示例：{', '.join(self.city_list[:10])}...")
            city_name = input("出生城市：").strip()
            if not city_name:
                city_name = "北京"
            
            return {
                'name': name,
                'gender': gender,
                'birth_year': birth_year,
                'birth_month': birth_month,
                'birth_day': birth_day,
                'birth_hour': birth_hour,
                'birth_minute': birth_minute,
                'city_name': city_name
            }
            
        except KeyboardInterrupt:
            print("\n\n用户取消输入")
            return None
        except Exception as e:
            print(f"输入错误: {e}")
            return None
    
    def run_analysis(self, user_info):
        """运行分析"""
        print("\n开始分析，请稍候...")
        print("-" * 40)
        
        try:
            result = self.analyzer.analyze_complete_bazi(
                birth_year=user_info['birth_year'],
                birth_month=user_info['birth_month'],
                birth_day=user_info['birth_day'],
                birth_hour=user_info['birth_hour'],
                birth_minute=user_info['birth_minute'],
                city_name=user_info['city_name'],
                name=user_info['name'],
                gender=user_info['gender']
            )
            
            if "错误" in result:
                print(f"分析失败：{result['错误']}")
                return False
            
            # 显示详细结果
            detailed_result = self.analyzer.format_comprehensive_output(result, show_details=True)
            print(detailed_result)

            # 询问是否保存结果
            save_choice = input("\n是否保存分析结果到文件？(y/n): ").strip().lower()
            if save_choice in ['y', 'yes', '是']:
                self.save_dual_results(user_info, result)
            
            return True
            
        except Exception as e:
            print(f"分析过程中出现错误: {e}")
            return False
    
    def save_dual_results(self, user_info, result):
        """保存双重分析结果：简洁版和详细版"""
        try:
            base_filename = f"{user_info['name']}_八字分析_{user_info['birth_year']}{user_info['birth_month']:02d}{user_info['birth_day']:02d}"

            # 保存简洁版结果
            simple_filename = f"{base_filename}_结果.txt"
            simple_result = self.analyzer.format_simple_output(result)
            with open(simple_filename, 'w', encoding='utf-8') as f:
                f.write(simple_result)

            # 保存详细版结果
            detailed_filename = f"{base_filename}_详细过程.txt"
            detailed_result = self.analyzer.format_comprehensive_output(result, show_details=True)
            with open(detailed_filename, 'w', encoding='utf-8') as f:
                f.write(detailed_result)

            print(f"分析结果已保存到:")
            print(f"  简洁版: {simple_filename}")
            print(f"  详细版: {detailed_filename}")

        except Exception as e:
            print(f"保存文件失败: {e}")

    def save_result(self, user_info, result):
        """保存分析结果（兼容旧版本）"""
        try:
            filename = f"{user_info['name']}_八字分析_{user_info['birth_year']}{user_info['birth_month']:02d}{user_info['birth_day']:02d}.txt"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(result)
            print(f"分析结果已保存到: {filename}")
        except Exception as e:
            print(f"保存文件失败: {e}")
    
    def run_interactive_mode(self):
        """运行交互模式"""
        self.display_welcome()
        
        while True:
            try:
                user_info = self.get_user_input()
                if user_info is None:
                    break
                
                success = self.run_analysis(user_info)
                
                if success:
                    print("\n" + "="*60)
                    choice = input("是否继续分析其他人的八字？(y/n): ").strip().lower()
                    if choice not in ['y', 'yes', '是']:
                        break
                else:
                    choice = input("是否重新输入？(y/n): ").strip().lower()
                    if choice not in ['y', 'yes', '是']:
                        break
                        
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
            except Exception as e:
                print(f"程序运行错误: {e}")
                break
        
        print("\n感谢使用八字占卜系统！")
    
    def run_api_mode(self, user_data):
        """API模式（为前端接口预留）"""
        try:
            result = self.analyzer.analyze_complete_bazi(
                birth_year=user_data['birth_year'],
                birth_month=user_data['birth_month'],
                birth_day=user_data['birth_day'],
                birth_hour=user_data['birth_hour'],
                birth_minute=user_data['birth_minute'],
                city_name=user_data['city_name'],
                name=user_data['name'],
                gender=user_data.get('gender', '男')
            )
            
            return self.analyzer.get_api_result(result)
            
        except Exception as e:
            return {"success": False, "error": str(e)}


def main():
    """主函数"""
    system = BaziSystem()
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == '--test':
            # 测试模式
            print("运行测试模式...")
            test_data = {
                'name': '测试用户',
                'gender': '男',
                'birth_year': 1990,
                'birth_month': 5,
                'birth_day': 15,
                'birth_hour': 15,
                'birth_minute': 0,
                'city_name': '广州'
            }

            result = system.run_api_mode(test_data)
            if result['success']:
                # API模式返回的数据结构需要转换
                api_data = result['data']
                comprehensive_data = {
                    "基本信息": api_data['basic_info'],
                    "五行分析": api_data['wuxing_analysis'],
                    "十神分析": api_data['shishen_analysis'],
                    "大运流年": api_data['dayun_analysis'],
                    "合婚分析": api_data['marriage_analysis']
                }
                formatted_result = system.analyzer.format_comprehensive_output(comprehensive_data)
                print(formatted_result)
            else:
                print(f"测试失败: {result['error']}")
        elif sys.argv[1] == '--simple':
            # 简洁版测试模式
            print("运行简洁版测试模式...")
            test_data = {
                'name': '测试用户',
                'gender': '男',
                'birth_year': 1990,
                'birth_month': 5,
                'birth_day': 15,
                'birth_hour': 15,
                'birth_minute': 0,
                'city_name': '广州'
            }

            result = system.run_api_mode(test_data)
            if result['success']:
                # API模式返回的数据结构需要转换
                api_data = result['data']
                comprehensive_data = {
                    "基本信息": api_data['basic_info'],
                    "五行分析": api_data['wuxing_analysis'],
                    "十神分析": api_data['shishen_analysis'],
                    "大运流年": api_data['dayun_analysis'],
                    "合婚分析": api_data['marriage_analysis']
                }
                # 使用简洁版输出
                formatted_result = system.analyzer.format_simple_output(comprehensive_data)
                print(formatted_result)
            else:
                print(f"测试失败: {result['error']}")
    else:
        # 交互模式
        system.run_interactive_mode()


if __name__ == "__main__":
    main()
