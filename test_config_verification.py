#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
验证config参数是否真正生效的测试
"""

from main import BaziSystem
import json

def test_config_verification():
    """验证config参数是否真正生效"""
    print("=" * 60)
    print("验证config参数是否真正生效")
    print("=" * 60)
    
    # 读取当前config
    with open('config.json', 'r', encoding='utf-8') as f:
        config = json.load(f)
    
    print("📋 当前config.json中的关键参数：")
    print("-" * 40)
    print(f"地支六冲: {config['地支关系影响系数']['地支六冲']}")
    print(f"天干五合: {config['地支关系影响系数']['天干五合']}")
    print(f"地支六合: {config['地支关系影响系数']['地支六合']}")
    print(f"相生: {config['地支关系影响系数']['相生']}")
    print(f"相克: {config['地支关系影响系数']['相克']}")
    
    system = BaziSystem()
    
    # 检查系统中加载的config参数
    analyzer_config = system.analyzer.config
    
    print("\n📊 系统中实际加载的参数：")
    print("-" * 40)
    print(f"地支六冲: {analyzer_config['地支关系影响系数']['地支六冲']}")
    print(f"天干五合: {analyzer_config['地支关系影响系数']['天干五合']}")
    print(f"地支六合: {analyzer_config['地支关系影响系数']['地支六合']}")
    print(f"相生: {analyzer_config['地支关系影响系数']['相生']}")
    print(f"相克: {analyzer_config['地支关系影响系数']['相克']}")
    
    # 验证参数一致性
    print("\n✅ 参数一致性验证：")
    print("-" * 40)
    
    checks = [
        ("地支六冲", analyzer_config['地支关系影响系数']['地支六冲'], config['地支关系影响系数']['地支六冲']),
        ("天干五合", analyzer_config['地支关系影响系数']['天干五合'], config['地支关系影响系数']['天干五合']),
        ("地支六合", analyzer_config['地支关系影响系数']['地支六合'], config['地支关系影响系数']['地支六合']),
        ("相生", analyzer_config['地支关系影响系数']['相生'], config['地支关系影响系数']['相生']),
        ("相克", analyzer_config['地支关系影响系数']['相克'], config['地支关系影响系数']['相克'])
    ]
    
    all_consistent = True
    for param_name, actual, expected in checks:
        if actual == expected:
            print(f"✅ {param_name}: {actual} (一致)")
        else:
            print(f"❌ {param_name}: 实际={actual}, 期望={expected} (不一致)")
            all_consistent = False
    
    if not all_consistent:
        print("\n⚠️ 发现参数不一致！需要检查config加载逻辑")
        return
    
    # 测试实际计算
    user_info = {
        'name': 'config参数验证测试',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'birth_location': '重庆'
    }
    
    try:
        # 直接调用分析器
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['birth_location'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        # 检查流年计算结果
        dayun_info = result.get('大运流年', {})
        liunian_result = dayun_info.get('流年分析', {})
        liunian_details = dayun_info.get('流年计算详情', {})
        
        print(f"\n🎯 流年计算结果：")
        print("-" * 40)
        print(f"流年：{liunian_result.get('流年', '未知')} ({liunian_result.get('流年干支', '未知')})")
        print(f"总得分：{liunian_result.get('总得分', 0):.2f}")
        print(f"吉凶等级：{liunian_result.get('吉凶等级', '未知')}")
        
        # 检查计算详情中是否使用了正确的参数
        print(f"\n🔍 计算详情中的参数使用：")
        print("-" * 40)
        
        if '第二步_流年与原局作用关系' in liunian_details:
            step2 = liunian_details['第二步_流年与原局作用关系']
            
            # 检查天干作用详情
            tiangan_zuoyong = step2.get('天干作用详情', [])
            for zuoyong in tiangan_zuoyong:
                if '天干五合' in zuoyong.get('五行关系', ''):
                    formula = zuoyong.get('计算公式', '')
                    print(f"天干五合计算公式: {formula}")
                    # 检查公式中是否包含正确的系数
                    if str(config['地支关系影响系数']['天干五合']) in formula:
                        print(f"✅ 天干五合系数 {config['地支关系影响系数']['天干五合']} 正确应用")
                    else:
                        print(f"❌ 天干五合系数未正确应用")
                elif '相生' in zuoyong.get('五行关系', ''):
                    formula = zuoyong.get('计算公式', '')
                    print(f"相生计算公式: {formula}")
                    if str(config['地支关系影响系数']['相生']) in formula:
                        print(f"✅ 相生系数 {config['地支关系影响系数']['相生']} 正确应用")
                    else:
                        print(f"❌ 相生系数未正确应用")
                elif '相克' in zuoyong.get('五行关系', ''):
                    formula = zuoyong.get('计算公式', '')
                    print(f"相克计算公式: {formula}")
                    if str(config['地支关系影响系数']['相克']) in formula:
                        print(f"✅ 相克系数 {config['地支关系影响系数']['相克']} 正确应用")
                    else:
                        print(f"❌ 相克系数未正确应用")
            
            # 检查地支作用详情
            dizhi_zuoyong = step2.get('地支作用详情', [])
            for zuoyong in dizhi_zuoyong:
                if '地支六冲' in zuoyong.get('五行关系', ''):
                    formula = zuoyong.get('计算公式', '')
                    print(f"地支六冲计算公式: {formula}")
                    if str(config['地支关系影响系数']['地支六冲']) in formula:
                        print(f"✅ 地支六冲系数 {config['地支关系影响系数']['地支六冲']} 正确应用")
                    else:
                        print(f"❌ 地支六冲系数未正确应用")
                elif '地支六合' in zuoyong.get('五行关系', ''):
                    formula = zuoyong.get('计算公式', '')
                    print(f"地支六合计算公式: {formula}")
                    if str(config['地支关系影响系数']['地支六合']) in formula:
                        print(f"✅ 地支六合系数 {config['地支关系影响系数']['地支六合']} 正确应用")
                    else:
                        print(f"❌ 地支六合系数未正确应用")
        
        # 保存详细结果
        formatted_result = system.analyzer.format_comprehensive_output(result, show_details=True)
        with open('config参数验证测试_八字分析_19991016_结果.txt', 'w', encoding='utf-8') as f:
            f.write(formatted_result)
        
        print('\n✅ config参数验证测试报告已生成：config参数验证测试_八字分析_19991016_结果.txt')
        
        print("\n" + "=" * 60)
        print("config参数验证测试完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_config_verification()
