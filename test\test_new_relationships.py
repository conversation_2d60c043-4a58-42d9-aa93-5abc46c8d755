#!/usr/bin/env python3
"""
测试新的特殊关系计算
"""

from main import BaziSystem

def test_new_relationships():
    """测试新的特殊关系计算"""
    system = BaziSystem()
    
    # 测试数据
    user_info = {
        'name': '测试新关系',
        'gender': '女',
        'birth_year': 1999,
        'birth_month': 10,
        'birth_day': 16,
        'birth_hour': 16,
        'birth_minute': 28,
        'city_name': '重庆'
    }
    
    print("开始测试新的特殊关系计算...")
    print(f"测试用户: {user_info['name']}")
    print(f"出生时间: {user_info['birth_year']}-{user_info['birth_month']}-{user_info['birth_day']} {user_info['birth_hour']}:{user_info['birth_minute']}")
    print(f"出生地点: {user_info['city_name']}")
    print("="*60)
    
    try:
        # 进行八字分析
        result = system.analyzer.analyze_complete_bazi(
            birth_year=user_info['birth_year'],
            birth_month=user_info['birth_month'],
            birth_day=user_info['birth_day'],
            birth_hour=user_info['birth_hour'],
            birth_minute=user_info['birth_minute'],
            city_name=user_info['city_name'],
            name=user_info['name'],
            gender=user_info['gender']
        )
        
        if "错误" in result:
            print(f"分析失败: {result['错误']}")
            return
        
        # 保存结果
        system.save_dual_results(user_info, result)
        
        print("\n测试完成！")
        print("请查看生成的文件，检查以下新功能：")
        print("1. 天干比劫帮扶关系 (权重+2.0)")
        print("2. 印星生扶关系 (权重+1.5)")
        print("3. 地支根气关系 (权重+2.0)")
        print("4. 神煞计算是否正确显示")
        print("5. 流年计算是否不再出现大量0分")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_new_relationships()
