"""
十神计算模块
实现十神识别、权重计算、身强身弱判断、喜用神忌神分析
"""

import json


class ShishenCalculator:
    def __init__(self, config_path="config.json"):
        """初始化十神计算器"""
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = json.load(f)
        
        # 天干五行阴阳属性
        self.tiangan_info = {
            '甲': {'五行': '木', '阴阳': '阳'},
            '乙': {'五行': '木', '阴阳': '阴'},
            '丙': {'五行': '火', '阴阳': '阳'},
            '丁': {'五行': '火', '阴阳': '阴'},
            '戊': {'五行': '土', '阴阳': '阳'},
            '己': {'五行': '土', '阴阳': '阴'},
            '庚': {'五行': '金', '阴阳': '阳'},
            '辛': {'五行': '金', '阴阳': '阴'},
            '壬': {'五行': '水', '阴阳': '阳'},
            '癸': {'五行': '水', '阴阳': '阴'}
        }
        
        # 地支藏干信息
        self.dizhi_canggan = {
            '子': {'癸': 1.0},
            '丑': {'己': 0.7, '癸': 0.2, '辛': 0.1},
            '寅': {'甲': 0.6, '丙': 0.3, '戊': 0.1},
            '卯': {'乙': 1.0},
            '辰': {'戊': 0.6, '乙': 0.3, '癸': 0.1},
            '巳': {'丙': 0.6, '戊': 0.3, '庚': 0.1},
            '午': {'丁': 0.7, '己': 0.3},
            '未': {'己': 0.6, '丁': 0.3, '乙': 0.1},
            '申': {'庚': 0.6, '壬': 0.3, '戊': 0.1},
            '酉': {'辛': 1.0},
            '戌': {'戊': 0.6, '辛': 0.3, '丁': 0.1},
            '亥': {'壬': 0.7, '甲': 0.3}
        }
        
        # 五行生克关系
        self.wuxing_sheng = {'木': '火', '火': '土', '土': '金', '金': '水', '水': '木'}
        self.wuxing_ke = {'木': '土', '土': '水', '水': '火', '火': '金', '金': '木'}
        
        # 配偶星对照表
        self.spouse_star = {
            '甲': {'男': {'正财': '己', '偏财': '戊'}, '女': {'正官': '辛', '七杀': '庚'}},
            '乙': {'男': {'正财': '戊', '偏财': '己'}, '女': {'正官': '庚', '七杀': '辛'}},
            '丙': {'男': {'正财': '辛', '偏财': '庚'}, '女': {'正官': '癸', '七杀': '壬'}},
            '丁': {'男': {'正财': '庚', '偏财': '辛'}, '女': {'正官': '壬', '七杀': '癸'}},
            '戊': {'男': {'正财': '癸', '偏财': '壬'}, '女': {'正官': '乙', '七杀': '甲'}},
            '己': {'男': {'正财': '壬', '偏财': '癸'}, '女': {'正官': '甲', '七杀': '乙'}},
            '庚': {'男': {'正财': '乙', '偏财': '甲'}, '女': {'正官': '丁', '七杀': '丙'}},
            '辛': {'男': {'正财': '甲', '偏财': '乙'}, '女': {'正官': '丙', '七杀': '丁'}},
            '壬': {'男': {'正财': '丁', '偏财': '丙'}, '女': {'正官': '己', '七杀': '戊'}},
            '癸': {'男': {'正财': '丙', '偏财': '丁'}, '女': {'正官': '戊', '七杀': '己'}}
        }
    
    def get_wuxing_relation(self, my_wuxing, other_wuxing):
        """获取五行生克关系"""
        if my_wuxing == other_wuxing:
            return '同我'
        elif self.wuxing_sheng.get(my_wuxing) == other_wuxing:
            return '我生'
        elif self.wuxing_sheng.get(other_wuxing) == my_wuxing:
            return '生我'
        elif self.wuxing_ke.get(my_wuxing) == other_wuxing:
            return '我克'
        elif self.wuxing_ke.get(other_wuxing) == my_wuxing:
            return '克我'
        else:
            return '无关'
    
    def determine_shishen(self, day_gan, other_gan):
        """确定十神类型"""
        day_info = self.tiangan_info[day_gan]
        other_info = self.tiangan_info[other_gan]
        
        relation = self.get_wuxing_relation(day_info['五行'], other_info['五行'])
        same_yinyang = day_info['阴阳'] == other_info['阴阳']
        
        if relation == '同我':
            return '比肩' if same_yinyang else '劫财'
        elif relation == '生我':
            return '偏印' if same_yinyang else '正印'
        elif relation == '克我':
            return '七杀' if same_yinyang else '正官'
        elif relation == '我克':
            return '偏财' if same_yinyang else '正财'
        elif relation == '我生':
            return '食神' if same_yinyang else '伤官'
        else:
            return '未知'
    
    def calculate_shishen_weights(self, sizhu):
        """计算十神权重"""
        day_gan = sizhu['日柱'][0]
        shishen_weights = {}

        # 分析每个位置的天干和藏干
        positions = [
            ('年柱', sizhu['年柱']),
            ('月柱', sizhu['月柱']),
            ('时柱', sizhu['时柱'])  # 日柱不分析自己
        ]

        for pos_name, (gan, zhi) in positions:
            # 天干分析
            if gan != day_gan:  # 不分析日干自己
                shishen = self.determine_shishen(day_gan, gan)
                if shishen not in shishen_weights:
                    shishen_weights[shishen] = 0

                weight = 1.0  # 天干基础权重为1
                # 月干权重提升：月柱天干权重乘以3
                if pos_name == '月柱':
                    weight = weight * 3

                shishen_weights[shishen] += weight

            # 地支藏干分析
            canggan_dict = self.dizhi_canggan[zhi]
            for canggan, base_weight in canggan_dict.items():
                if canggan != day_gan:  # 不分析日干自己
                    shishen = self.determine_shishen(day_gan, canggan)
                    if shishen not in shishen_weights:
                        shishen_weights[shishen] = 0

                    final_weight = base_weight

                    # 月支权重提升：月柱地支藏干权重乘以3
                    if pos_name == '月柱':
                        final_weight = final_weight * 3

                    shishen_weights[shishen] += final_weight

        # 分析日支藏干（除了日干本身）
        day_zhi = sizhu['日柱'][1]
        day_canggan = self.dizhi_canggan[day_zhi]
        for canggan, base_weight in day_canggan.items():
            if canggan != day_gan:
                shishen = self.determine_shishen(day_gan, canggan)
                if shishen not in shishen_weights:
                    shishen_weights[shishen] = 0

                final_weight = base_weight
                # 透干处理：如果藏干与日柱天干一致，权重乘以2
                # 注意：这里canggan != day_gan，所以不会进入透干逻辑
                # 透干逻辑应该在上面的循环中处理

                shishen_weights[shishen] += final_weight

        # 处理透干情况：检查所有地支藏干是否与日干一致
        all_positions = [
            ('年柱', sizhu['年柱'][1]),
            ('月柱', sizhu['月柱'][1]),
            ('日柱', sizhu['日柱'][1]),
            ('时柱', sizhu['时柱'][1])
        ]

        for pos_name, zhi in all_positions:
            canggan_dict = self.dizhi_canggan[zhi]
            for canggan, base_weight in canggan_dict.items():
                if canggan == day_gan:  # 透干情况
                    # 透干不计入十神，但会影响身强身弱
                    # 这里我们需要为比肩增加权重（因为透干相当于增强日主力量）
                    shishen = '比肩'  # 透干相当于比肩
                    if shishen not in shishen_weights:
                        shishen_weights[shishen] = 0

                    final_weight = base_weight * 2  # 透干权重乘以2

                    # 月支透干权重再乘以3
                    if pos_name == '月柱':
                        final_weight = final_weight * 3

                    shishen_weights[shishen] += final_weight

        # 排序
        sorted_shishen = sorted(shishen_weights.items(), key=lambda x: x[1], reverse=True)

        return dict(sorted_shishen)
    
    def analyze_body_strength(self, shishen_weights):
        """分析身强身弱"""
        # 生扶日主的力量：正印、偏印、比肩、劫财
        support_power = (
            shishen_weights.get('正印', 0) +
            shishen_weights.get('偏印', 0) +
            shishen_weights.get('比肩', 0) +
            shishen_weights.get('劫财', 0)
        )
        
        # 克泄耗日主的力量：正官、七杀、食神、伤官、正财、偏财
        drain_power = (
            shishen_weights.get('正官', 0) +
            shishen_weights.get('七杀', 0) +
            shishen_weights.get('食神', 0) +
            shishen_weights.get('伤官', 0) +
            shishen_weights.get('正财', 0) +
            shishen_weights.get('偏财', 0)
        )
        
        if support_power > drain_power:
            return '身强', support_power, drain_power
        else:
            return '身弱', support_power, drain_power
    
    def determine_xiyongshen(self, body_strength, shishen_weights):
        """确定喜用神和忌神"""
        if body_strength == '身强':
            # 身强需要克泄耗
            xiyong_types = ['正官', '七杀', '食神', '伤官', '正财', '偏财']
            ji_types = ['正印', '偏印', '比肩', '劫财']
        else:
            # 身弱需要生扶
            xiyong_types = ['正印', '偏印', '比肩', '劫财']
            ji_types = ['正官', '七杀', '食神', '伤官', '正财', '偏财']
        
        # 按权重排序喜用神
        xiyong_list = []
        for shishen_type in xiyong_types:
            if shishen_type in shishen_weights:
                xiyong_list.append((shishen_type, shishen_weights[shishen_type]))
        
        ji_list = []
        for shishen_type in ji_types:
            if shishen_type in shishen_weights:
                ji_list.append((shishen_type, shishen_weights[shishen_type]))
        
        return xiyong_list, ji_list
    
    def analyze_spouse_info(self, sizhu, gender):
        """分析夫妻宫和配偶星"""
        day_gan = sizhu['日柱'][0]
        day_zhi = sizhu['日柱'][1]  # 夫妻宫
        
        # 配偶星
        spouse_stars = self.spouse_star[day_gan][gender]
        
        # 在八字中寻找配偶星
        spouse_star_found = {}
        all_gans = [sizhu['年柱'][0], sizhu['月柱'][0], sizhu['时柱'][0]]
        
        # 加上藏干
        for zhi in [sizhu['年柱'][1], sizhu['月柱'][1], sizhu['日柱'][1], sizhu['时柱'][1]]:
            canggan_dict = self.dizhi_canggan[zhi]
            for gan, weight in canggan_dict.items():
                all_gans.append(gan)
        
        for star_type, star_gan in spouse_stars.items():
            count = all_gans.count(star_gan)
            if count > 0:
                spouse_star_found[star_type] = count
        
        return {
            '夫妻宫': day_zhi,
            '配偶星': spouse_star_found
        }
    
    def format_shishen_output(self, sizhu, shishen_weights, body_analysis, 
                             xiyong_info, spouse_info, gender='男'):
        """格式化十神分析输出"""
        body_strength, support_power, drain_power = body_analysis
        xiyong_list, ji_list = xiyong_info
        
        output = f"""
=== 十神分析结果 ===
日主：{sizhu['日柱'][0]} ({self.tiangan_info[sizhu['日柱'][0]]['五行']}{self.tiangan_info[sizhu['日柱'][0]]['阴阳']})

十神权重分布：
"""
        for shishen, weight in shishen_weights.items():
            output += f"{shishen}：{weight:.2f}\n"
        
        output += f"""
身强身弱分析：
判定结果：{body_strength}
生扶力量：{support_power:.2f}
克泄耗力量：{drain_power:.2f}

喜用神：
"""
        for shishen, weight in xiyong_list[:4]:  # 显示前4个
            output += f"{shishen} ({weight:.2f}) "
        
        output += f"""

忌神：
"""
        for shishen, weight in ji_list[:4]:  # 显示前4个
            output += f"{shishen} ({weight:.2f}) "
        
        output += f"""

夫妻宫配偶星分析：
夫妻宫：{spouse_info['夫妻宫']}
配偶星：{spouse_info['配偶星']}
"""
        
        return output


# 测试函数
def test_shishen_calculator():
    """测试十神计算模块"""
    from calendar_converter import CalendarConverter
    
    # 先获取四柱信息
    converter = CalendarConverter()
    result = converter.process_birth_info(
        birth_year=1990,
        birth_month=5,
        birth_day=15,
        birth_hour=14,
        birth_minute=30,
        city_name="广州",
        name="测试用户"
    )
    
    # 计算十神
    calculator = ShishenCalculator()
    shishen_weights = calculator.calculate_shishen_weights(result['四柱'])
    body_analysis = calculator.analyze_body_strength(shishen_weights)
    xiyong_info = calculator.determine_xiyongshen(body_analysis[0], shishen_weights)
    spouse_info = calculator.analyze_spouse_info(result['四柱'], '男')
    
    print(calculator.format_shishen_output(
        result['四柱'], shishen_weights, body_analysis, 
        xiyong_info, spouse_info, '男'
    ))
    
    return shishen_weights, body_analysis, xiyong_info


if __name__ == "__main__":
    test_shishen_calculator()
